const fs = require('fs');
const fsPromises = require('fs').promises;
const path = require('path');
const { app } = require('electron');
const crypto = require('crypto');

class ConfigManager {
    constructor() {
        this.configPath = path.join(app.getPath('userData'), 'config.json');
        this.mcpConfigPath = path.join(__dirname, 'mcp_desktop_config.json');
        this.config = null;
        this.encryptionKey = this.getEncryptionKey();
        this.ivLength = 16; // IV 길이 추가
        console.log('ConfigManager initialized with paths:');
        console.log('  - Main config:', this.configPath);
        console.log('  - MCP config:', this.mcpConfigPath);
    }

    // 암호화 키 생성 또는 가져오기
    getEncryptionKey() {
        const keyPath = path.join(app.getPath('userData'), '.key');
        try {
            // 키 파일이 존재하는지 확인
            if (fs.existsSync(keyPath)) {
                console.log('Loading existing encryption key');
                return fs.readFileSync(keyPath, 'utf8');
            } else {
                // 새 키 생성
                console.log('Generating new encryption key');
                const key = crypto.randomBytes(32).toString('hex');
                fs.writeFileSync(keyPath, key, { mode: 0o600 }); // 소유자만 읽기/쓰기 가능
                return key;
            }
        } catch (error) {
            console.error('Error with encryption key:', error);
            // 폴백 키 (덜 안전하지만 작동은 함)
            return app.getPath('userData');
        }
    }

    // 문자열 암호화
    encryptString(text) {
        try {
            if (!text) return '';
            
            console.log('Encrypting string');
            const iv = crypto.randomBytes(this.ivLength);
            const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(this.encryptionKey, 'hex'), iv);
            
            let encrypted = cipher.update(text, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            // IV와 암호화된 텍스트를 함께 저장 (IV는 암호화마다 달라야 함)
            const result = iv.toString('hex') + ':' + encrypted;
            console.log('String encrypted successfully');
            return result;
        } catch (error) {
            console.error('Encryption error:', error);
            // 암호화 실패 시 원본 반환 (보안상 좋지 않지만 기능 유지를 위함)
            return 'ENCRYPTION_FAILED:' + text;
        }
    }

    // 문자열 복호화
    decryptString(encryptedText) {
        try {
            if (!encryptedText) return '';
            
            console.log('Decrypting string');
            
            // 암호화 실패 표시가 있는 경우
            if (encryptedText.startsWith('ENCRYPTION_FAILED:')) {
                console.log('Found encryption failure marker, returning original text');
                return encryptedText.substring('ENCRYPTION_FAILED:'.length);
            }
            
            // IV와 암호화된 텍스트 분리
            const parts = encryptedText.split(':');
            if (parts.length !== 2) {
                console.error('Invalid encrypted format');
                return '';
            }
            
            const iv = Buffer.from(parts[0], 'hex');
            const encrypted = parts[1];
            
            const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(this.encryptionKey, 'hex'), iv);
            
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            console.log('String decrypted successfully');
            return decrypted;
        } catch (error) {
            console.error('Decryption error:', error);
            return '';
        }
    }

    getDefaultConfig() {
        return {
            theme: 'dark',
            language: 'ko',
            llmConfig: {
                apiType: 'openai', // 'openai', 'anthropic', 'ollama', 'llamacpp', 'custom'
                apiUrl: 'https://api.openai.com',
                encryptedApiKey: '',
                customApiUrl: '',
                model: 'gpt-3.5-turbo',
                temperature: 0.7,
                maxTokens: 2048,
                // API 타입별 기본 설정
                apiTypeDefaults: {
                    openai: {
                        apiUrl: 'https://api.openai.com',
                        endpoints: ['/v1/chat/completions'],
                        models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o']
                    },
                    anthropic: {
                        apiUrl: 'https://api.anthropic.com',
                        endpoints: ['/v1/messages'],
                        models: ['claude-3-sonnet-20240229', 'claude-3-opus-20240229', 'claude-3-haiku-20240307']
                    },
                    ollama: {
                        apiUrl: 'http://localhost:11434',
                        endpoints: ['/api/chat', '/api/generate'],
                        models: ['llama2', 'codellama', 'mistral', 'llama4-maverick']
                    },
                    llamacpp: {
                        apiUrl: 'http://localhost:8080',
                        endpoints: ['/v1/chat/completions', '/completion'],
                        models: []
                    },
                    custom: {
                        apiUrl: '',
                        endpoints: ['/v1/chat/completions', '/completion', '/generate'],
                        models: []
                    },
                    // 하위 호환성을 위한 별칭
                    local: {
                        apiUrl: 'http://localhost:11434',
                        endpoints: ['/api/chat', '/api/generate'],
                        models: ['llama2', 'codellama', 'mistral', 'llama4-maverick']
                    },
                    llama: {
                        apiUrl: 'http://localhost:8080',
                        endpoints: ['/v1/chat/completions', '/completion'],
                        models: []
                    }
                },
                // MCP 서버 연결 설정
                mcpEnabled: true,
                mcpConnections: {
                    'internal-filesystem': {
                        url: 'ws://localhost:3001',
                        enabled: true,
                        autoConnect: true
                    },
                    'internal-main': {
                        url: 'ws://localhost:3000',
                        enabled: true,
                        autoConnect: true
                    },
                    'filesystem': {
                        url: 'ws://localhost:3002',
                        enabled: true,
                        autoConnect: true
                    }
                },
                // 프롬프트 설정
                promptSettings: {
                    systemPrompt: '당신은 도움이 되고 정확한 AI 어시스턴트입니다. 사용자의 질문에 친절하고 상세하게 답변해주세요.',
                    userPromptTemplate: '{user_input}',
                    conversationStarters: [
                        '안녕하세요! 무엇을 도와드릴까요?',
                        '오늘 어떤 것에 대해 이야기하고 싶으신가요?',
                        '궁금한 것이 있으시면 언제든 물어보세요!'
                    ],
                    enableSystemPrompt: true,
                    enablePromptTemplate: false
                }
            },
            appearance: {
                fontSize: '14',
                fontFamily: 'system'
            },
            general: {
                startAtLogin: false,
                minimizeToTray: true
            },
            advanced: {
                enableDevTools: false,
                enableLogging: false
            },
            // Claude Desktop 호환 MCP 서버 설정 형식
            mcpServers: {
                // 파일시스템 서버 (Claude Desktop 호환)
                "filesystem": {
                    "command": "npx",
                    "args": [
                        "-y",
                        "@modelcontextprotocol/server-filesystem",
                        process.cwd()
                    ],
                    "env": {},
                    "enabled": true,
                    "description": "파일 시스템 접근을 위한 MCP 서버"
                },
                // 내장 파일시스템 서버 (기본 활성화)
                "internal-filesystem": {
                    "command": "node",
                    "args": ["./file-server.js", process.cwd()],
                    "env": {
                        "NODE_ENV": "production",
                        "MCP_PORT": "3001"
                    },
                    "internal": true,
                    "enabled": true,
                    "description": "내장 파일시스템 서버"
                },
                // 내장 메인 MCP 서버 (기본 활성화)
                "internal-main": {
                    "command": "node",
                    "args": ["./mcp-server.js"],
                    "env": {
                        "NODE_ENV": "production",
                        "MCP_PORT": "3000"
                    },
                    "internal": true,
                    "enabled": true,
                    "description": "내장 메인 MCP 서버"
                },
                // 공식 파일시스템 서버 (Claude Desktop과 동일) - 기본 활성화
                "filesystem": {
                    "command": "npx",
                    "args": [
                        "-y",
                        "@modelcontextprotocol/server-filesystem",
                        process.cwd(),
                        require('os').homedir(),
                        require('path').join(require('os').homedir(), 'Desktop'),
                        require('path').join(require('os').homedir(), 'Documents'),
                        require('path').join(require('os').homedir(), 'Downloads')
                    ],
                    "env": {}
                }
                // 추가 서버 예시 (필요시 주석 해제)
                // "git": {
                //     "command": "uvx",
                //     "args": ["mcp-server-git", "--repository", "path/to/git/repo"],
                //     "env": {}
                // }
            },
            windowState: {
                width: 1200,
                height: 800,
                x: undefined,
                y: undefined,
                maximized: false
            }
        };
    }

    async loadConfig() {
        try {
            console.log('Loading config from:', this.configPath);
            
            // 파일이 존재하는지 확인
            let fileExists = false;
            try {
                await fsPromises.access(this.configPath, fs.constants.F_OK);
                fileExists = true;
                console.log('Config file exists');
            } catch (accessError) {
                console.log('Config file does not exist, will create default config');
                fileExists = false;
            }

            // 기본 설정 준비
            const defaultConfig = this.getDefaultConfig();
            
            if (!fileExists) {
                // 파일이 없으면 기본 설정으로 생성
                console.log('Creating default config file');
                this.config = defaultConfig;
                try {
                    await this.saveConfig(this.config);
                    console.log('Default config saved successfully');
                } catch (saveError) {
                    console.error('Failed to save default config, but continuing:', saveError);
                    // 저장 실패해도 기본 설정 반환
                }
                return this.config;
            }

            // 파일 읽기
            let configData;
            try {
                configData = await fsPromises.readFile(this.configPath, 'utf8');
                console.log('Config file read successfully');
            } catch (readError) {
                console.error('Failed to read config file:', readError);
                this.config = defaultConfig;
                return this.config;
            }
            
            // 빈 파일이면 기본 설정 사용
            if (!configData || !configData.trim()) {
                console.log('Config file is empty, using defaults');
                this.config = defaultConfig;
                try {
                    await this.saveConfig(this.config);
                } catch (saveError) {
                    console.error('Failed to save default config to empty file:', saveError);
                }
                return this.config;
            }
            
            // JSON 파싱
            try {
                const parsedConfig = JSON.parse(configData);
                console.log('Config file parsed successfully');
                // 기본 설정과 병합
                this.config = { ...defaultConfig, ...parsedConfig };
                return this.config;
            } catch (parseError) {
                console.error('Failed to parse config file:', parseError);
                this.config = defaultConfig;
                try {
                    // 손상된 파일 백업
                    const backupPath = `${this.configPath}.backup.${Date.now()}`;
                    await fsPromises.copyFile(this.configPath, backupPath);
                    console.log(`Corrupted config file backed up to ${backupPath}`);
                    
                    // 새 기본 설정 저장
                    await this.saveConfig(defaultConfig);
                    console.log('Default config saved after parse error');
                } catch (backupError) {
                    console.error('Failed to backup corrupted config file:', backupError);
                }
                return this.config;
            }
        } catch (error) {
            console.error('Unexpected error loading config:', error);
            // 어떤 오류가 발생하더라도 기본 설정 반환
            this.config = this.getDefaultConfig();
            return this.config;
        }
    }

    async saveConfig(config) {
        try {
            console.log('Saving config to:', this.configPath);
            
            // 설정 객체 병합 (초기화 시에는 병합하지 않도록 수정)
            if (config === this.getDefaultConfig()) {
                this.config = config;
                console.log('Using default config directly');
            } else {
                // 기존 설정과 새 설정 병합
                const currentConfig = await this.loadConfig().catch(() => this.getDefaultConfig());
                this.config = { ...currentConfig, ...config };
                console.log('Merged with existing config');
            }
            
            // 설정 디렉토리 확인 및 생성
            const configDir = path.dirname(this.configPath);
            try {
                if (!fs.existsSync(configDir)) {
                    console.log('Creating config directory:', configDir);
                    await fsPromises.mkdir(configDir, { recursive: true });
                }
            } catch (dirError) {
                console.error('Failed to create config directory:', dirError);
                throw new Error(`설정 디렉토리를 생성할 수 없습니다: ${dirError.message}`);
            }
            
            // 설정 파일 저장
            try {
                const configJson = JSON.stringify(this.config, null, 2);
                await fsPromises.writeFile(this.configPath, configJson, 'utf8');
                console.log('Config saved successfully');
                return true;
            } catch (writeError) {
                console.error('Failed to write config file:', writeError);
                throw new Error(`설정 파일을 저장할 수 없습니다: ${writeError.message}`);
            }
        } catch (error) {
            console.error('Failed to save config:', error);
            throw error;
        }
    }

    // API 키 저장 (암호화)
    async saveApiKey(apiKey) {
        try {
            console.log('Saving API key to config');
            
            if (!this.config) {
                console.log('Config not loaded, loading now');
                await this.loadConfig();
            }
            
            if (!this.config.llmConfig) {
                this.config.llmConfig = {};
            }
            
            if (!apiKey) {
                console.log('No API key provided, clearing existing key');
                this.config.llmConfig.encryptedApiKey = '';
            } else {
                console.log('Encrypting and saving API key');
                this.config.llmConfig.encryptedApiKey = this.encryptString(apiKey);
            }
            
            console.log('Saving config with encrypted API key');
            await this.saveConfig(this.config);
            console.log('API key saved successfully');
            return true;
        } catch (error) {
            console.error('Error saving API key:', error);
            throw error;
        }
    }

    // API 키 가져오기 (복호화)
    getApiKey() {
        try {
            console.log('Getting API key from config');
            
            if (!this.config) {
                console.log('Config not loaded, loading now');
                this.loadConfig();
            }
            
            if (!this.config || !this.config.llmConfig || !this.config.llmConfig.encryptedApiKey) {
                console.log('No encrypted API key found in config');
                return '';
            }
            
            console.log('Encrypted API key found, decrypting');
            const decrypted = this.decryptString(this.config.llmConfig.encryptedApiKey);
            console.log('API key decrypted successfully:', !!decrypted);
            return decrypted;
        } catch (error) {
            console.error('Error getting API key:', error);
            return '';
        }
    }

    getConfig() {
        return this.config || this.getDefaultConfig();
    }

    async updateConfig(updates) {
        if (!this.config) {
            await this.loadConfig();
        }
        
        this.config = { ...this.config, ...updates };
        await this.saveConfig(this.config);
        return this.config;
    }

    // 설정 초기화 메서드 추가
    async resetConfig() {
        try {
            console.log('Resetting configuration to defaults');
            this.config = this.getDefaultConfig();

            // 설정 파일 저장
            await this.saveConfig(this.config);

            console.log('Configuration reset successfully');
            return true;
        } catch (error) {
            console.error('Failed to reset configuration:', error);
            throw error;
        }
    }

    // MCP 설정 전용 로드 메서드
    async loadMcpConfig() {
        try {
            console.log('Loading MCP config from:', this.mcpConfigPath);

            // 파일이 존재하는지 확인
            let fileExists = false;
            try {
                await fsPromises.access(this.mcpConfigPath, fs.constants.F_OK);
                fileExists = true;
                console.log('MCP config file exists');
            } catch (accessError) {
                console.log('MCP config file does not exist, will create default');
                fileExists = false;
            }

            if (fileExists) {
                const data = await fsPromises.readFile(this.mcpConfigPath, 'utf8');
                const mcpConfig = JSON.parse(data);
                console.log('MCP config loaded successfully');
                return mcpConfig;
            } else {
                // 기본 MCP 설정 생성 (Claude Desktop 표준 형식)
                const defaultMcpConfig = {
                    mcpServers: {
                        "filesystem": {
                            "command": "npx",
                            "args": [
                                "-y",
                                "@modelcontextprotocol/server-filesystem",
                                require('path').join(require('os').homedir(), 'Documents'),
                                require('path').join(require('os').homedir(), 'Downloads')
                            ]
                        }
                    }
                };

                await this.saveMcpConfig(defaultMcpConfig);
                console.log('Default MCP config created');
                return defaultMcpConfig;
            }
        } catch (error) {
            console.error('Error loading MCP config:', error);
            // 오류 발생 시 기본 설정 반환
            return {
                mcpServers: {}
            };
        }
    }

    // MCP 설정 전용 저장 메서드
    async saveMcpConfig(mcpConfig) {
        try {
            console.log('Saving MCP config to:', this.mcpConfigPath);

            // 디렉토리가 존재하는지 확인하고 생성
            const configDir = path.dirname(this.mcpConfigPath);
            try {
                await fsPromises.access(configDir, fs.constants.F_OK);
            } catch (dirError) {
                await fsPromises.mkdir(configDir, { recursive: true });
                console.log('Created config directory:', configDir);
            }

            // JSON 형식으로 저장
            const jsonData = JSON.stringify(mcpConfig, null, 2);
            await fsPromises.writeFile(this.mcpConfigPath, jsonData, 'utf8');

            console.log('MCP config saved successfully');
            return true;
        } catch (error) {
            console.error('Failed to save MCP config:', error);
            throw error;
        }
    }

    // MCP 서버 관리 메서드들
    async addMcpServer(name, serverConfig) {
        try {
            // MCP 설정 로드
            const mcpConfig = await this.loadMcpConfig();

            if (!mcpConfig.mcpServers) {
                mcpConfig.mcpServers = {};
            }

            // Claude Desktop 표준 형식으로만 저장 (모든 서버)
            mcpConfig.mcpServers[name] = {
                command: serverConfig.command,
                args: serverConfig.args || []
            };

            // env가 있고 비어있지 않은 경우에만 추가
            if (serverConfig.env && Object.keys(serverConfig.env).length > 0) {
                mcpConfig.mcpServers[name].env = serverConfig.env;
            }

            await this.saveMcpConfig(mcpConfig);
            console.log(`MCP server '${name}' added successfully to mcp_desktop_config.json`);
            return true;
        } catch (error) {
            console.error(`Failed to add MCP server '${name}':`, error);
            throw error;
        }
    }

    async removeMcpServer(name) {
        try {
            // MCP 설정 로드
            const mcpConfig = await this.loadMcpConfig();

            if (mcpConfig.mcpServers && mcpConfig.mcpServers[name]) {
                delete mcpConfig.mcpServers[name];
                await this.saveMcpConfig(mcpConfig);
                console.log(`MCP server '${name}' removed successfully from mcp_desktop_config.json`);
                return true;
            }

            return false;
        } catch (error) {
            console.error(`Failed to remove MCP server '${name}':`, error);
            throw error;
        }
    }

    async updateMcpServer(name, updates) {
        try {
            // MCP 설정 로드
            const mcpConfig = await this.loadMcpConfig();

            if (mcpConfig.mcpServers && mcpConfig.mcpServers[name]) {
                mcpConfig.mcpServers[name] = {
                    ...mcpConfig.mcpServers[name],
                    ...updates
                };
                await this.saveMcpConfig(mcpConfig);
                console.log(`MCP server '${name}' updated successfully in mcp_desktop_config.json`);
                return true;
            }

            return false;
        } catch (error) {
            console.error(`Failed to update MCP server '${name}':`, error);
            throw error;
        }
    }

    async getMcpServers() {
        try {
            const mcpConfig = await this.loadMcpConfig();
            return mcpConfig.mcpServers || {};
        } catch (error) {
            console.error('Failed to get MCP servers:', error);
            return {};
        }
    }

    async getMcpServer(name) {
        try {
            const servers = await this.getMcpServers();
            return servers[name] || null;
        } catch (error) {
            console.error(`Failed to get MCP server '${name}':`, error);
            return null;
        }
    }
}

module.exports = ConfigManager;
