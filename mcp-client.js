// MCP 클라이언트 - <PERSON> 호환 JSON-RPC 통신
const { EventEmitter } = require('events');
const { spawn } = require('child_process');
const WebSocket = require('ws');

class MCPClient extends EventEmitter {
    constructor(serverConfig) {
        super();
        this.serverConfig = serverConfig;
        this.process = null;
        this.websocket = null;
        this.connected = false;
        this.requestId = 0;
        this.pendingRequests = new Map();
        this.tools = new Map();
        this.resources = new Map();
        this.prompts = new Map();
        this.serverCapabilities = {};
        
        // 통신 방식 결정 (stdio 또는 WebSocket)
        this.communicationType = this.determineCommunicationType();
    }

    determineCommunicationType() {
        // WebSocket URL이 있으면 WebSocket 사용
        if (this.serverConfig.websocketUrl) {
            return 'websocket';
        }
        // 그렇지 않으면 stdio 사용
        return 'stdio';
    }

    async connect() {
        try {
            console.log(`🔌 Connecting to MCP server via ${this.communicationType}...`);
            console.log(`📋 Server config:`, {
                command: this.serverConfig.command,
                args: this.serverConfig.args,
                env: Object.keys(this.serverConfig.env || {})
            });

            if (this.communicationType === 'websocket') {
                await this.connectWebSocket();
            } else {
                await this.connectStdio();
            }

            // 초기화 핸드셰이크
            console.log('📡 Starting MCP protocol initialization...');
            await this.initialize();

            this.connected = true;
            this.emit('connected');

            console.log('✅ MCP client connected successfully');
            console.log(`📊 Server capabilities:`, Object.keys(this.serverCapabilities));
            console.log(`🔧 Available tools: ${this.tools.size}`);
            console.log(`📁 Available resources: ${this.resources.size}`);
            console.log(`💬 Available prompts: ${this.prompts.size}`);

        } catch (error) {
            console.error('❌ Failed to connect MCP client:', error);
            console.error('🔍 Connection details:', {
                command: this.serverConfig.command,
                args: this.serverConfig.args,
                communicationType: this.communicationType,
                error: error.message,
                stack: error.stack
            });
            this.emit('error', error);
            throw error;
        }
    }

    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            this.websocket = new WebSocket(this.serverConfig.websocketUrl);
            
            this.websocket.on('open', () => {
                console.log('WebSocket connection established');
                resolve();
            });
            
            this.websocket.on('message', (data) => {
                this.handleMessage(data.toString());
            });
            
            this.websocket.on('error', (error) => {
                console.error('WebSocket error:', error);
                reject(error);
            });
            
            this.websocket.on('close', () => {
                console.log('WebSocket connection closed');
                this.connected = false;
                this.emit('disconnected');
            });
        });
    }

    async connectStdio() {
        return new Promise((resolve, reject) => {
            // 기존에 실행 중인 서버 프로세스를 찾기
            const serverName = this.serverConfig.serverName || 'filesystem';

            // 전역 MCP 서버 프로세스 맵에서 찾기 (main.js에서 관리)
            if (global.mcpServerProcesses && global.mcpServerProcesses.has(serverName)) {
                console.log(`Connecting to existing MCP server process: ${serverName}`);
                this.process = global.mcpServerProcesses.get(serverName);
            } else {
                // 서버 프로세스가 없으면 새로 시작
                const { command, args = [], env = {} } = this.serverConfig;

                console.log(`Starting new MCP server process: ${command} ${args.join(' ')}`);

                this.process = spawn(command, args, {
                    env: { ...process.env, ...env },
                    stdio: ['pipe', 'pipe', 'pipe']
                });

                // 전역 맵에 등록
                if (global.mcpServerProcesses) {
                    global.mcpServerProcesses.set(serverName, this.process);
                }
            }

            // 프로세스가 이미 실행 중이면 바로 연결
            if (this.process.pid) {
                console.log(`MCP server process already running with PID: ${this.process.pid}`);
                resolve();
            } else {
                this.process.on('spawn', () => {
                    console.log('MCP server process spawned');
                    resolve();
                });
            }

            this.process.on('error', (error) => {
                console.error('MCP server process error:', error);
                reject(error);
            });

            this.process.on('exit', (code, signal) => {
                console.log(`MCP server process exited with code ${code}, signal ${signal}`);
                this.connected = false;
                this.emit('disconnected');
            });

            // stdout에서 JSON-RPC 메시지 읽기
            let buffer = '';
            this.process.stdout.on('data', (data) => {
                buffer += data.toString();

                // 줄 단위로 메시지 처리
                const lines = buffer.split('\n');
                buffer = lines.pop(); // 마지막 불완전한 줄은 버퍼에 유지

                for (const line of lines) {
                    if (line.trim()) {
                        this.handleMessage(line.trim());
                    }
                }
            });

            this.process.stderr.on('data', (data) => {
                console.error('MCP server stderr:', data.toString());
            });
        });
    }

    handleMessage(messageStr) {
        try {
            const message = JSON.parse(messageStr);

            // JSON-RPC 2.0 프로토콜 검증
            if (!message.jsonrpc || message.jsonrpc !== '2.0') {
                console.warn('Invalid JSON-RPC version in MCP message:', message.jsonrpc);
                return;
            }

            if (message.id && this.pendingRequests.has(message.id)) {
                // 응답 메시지
                const { resolve, reject } = this.pendingRequests.get(message.id);
                this.pendingRequests.delete(message.id);

                if (message.error) {
                    const error = new Error(message.error.message || 'MCP server error');
                    error.code = message.error.code;
                    error.data = message.error.data;
                    reject(error);
                } else {
                    resolve(message.result);
                }
            } else if (message.method) {
                // 알림 또는 요청 메시지
                this.handleNotification(message);
            } else {
                console.warn('Invalid MCP message format:', message);
            }
        } catch (error) {
            console.error('Failed to parse MCP message:', error, 'Raw message:', messageStr);
            this.emit('error', error);
        }
    }

    handleNotification(message) {
        switch (message.method) {
            case 'notifications/tools/list_changed':
                this.emit('tools_changed');
                break;
            case 'notifications/resources/list_changed':
                this.emit('resources_changed');
                break;
            case 'notifications/prompts/list_changed':
                this.emit('prompts_changed');
                break;
            default:
                console.log('Unhandled MCP notification:', message.method);
        }
    }

    async sendRequest(method, params = {}) {
        return new Promise((resolve, reject) => {
            const id = ++this.requestId;
            const request = {
                jsonrpc: '2.0',
                id,
                method,
                params
            };

            this.pendingRequests.set(id, { resolve, reject });

            const requestStr = JSON.stringify(request) + '\n';
            
            if (this.communicationType === 'websocket' && this.websocket) {
                this.websocket.send(requestStr);
            } else if (this.communicationType === 'stdio' && this.process) {
                this.process.stdin.write(requestStr);
            } else {
                reject(new Error('No active connection'));
                return;
            }

            // 타임아웃 설정 (30초)
            setTimeout(() => {
                if (this.pendingRequests.has(id)) {
                    this.pendingRequests.delete(id);
                    reject(new Error('Request timeout'));
                }
            }, 30000);
        });
    }

    sendNotification(method, params = {}) {
        const notification = {
            jsonrpc: '2.0',
            method,
            params
        };

        const notificationStr = JSON.stringify(notification) + '\n';

        if (this.communicationType === 'websocket' && this.websocket) {
            this.websocket.send(notificationStr);
        } else if (this.communicationType === 'stdio' && this.process) {
            this.process.stdin.write(notificationStr);
        } else {
            console.error('No active connection for notification');
        }
    }

    async initialize() {
        try {
            console.log('Initializing MCP client connection...');

            const result = await this.sendRequest('initialize', {
                protocolVersion: '2024-11-05',
                capabilities: {
                    roots: {
                        listChanged: true
                    },
                    sampling: {}
                },
                clientInfo: {
                    name: 'mcp-desktop-clone',
                    version: '1.0.0'
                }
            });

            // 서버 응답 검증
            if (!result.protocolVersion) {
                throw new Error('Server did not provide protocol version');
            }

            if (result.protocolVersion !== '2024-11-05') {
                console.warn(`Protocol version mismatch. Client: 2024-11-05, Server: ${result.protocolVersion}`);
            }

            this.serverCapabilities = result.capabilities || {};
            console.log('MCP server capabilities:', this.serverCapabilities);
            console.log('MCP server info:', result.serverInfo);

            // 초기화 완료 알림 (알림이므로 ID 없이 전송)
            this.sendNotification('notifications/initialized');

            // 도구, 리소스, 프롬프트 목록 가져오기
            await this.refreshLists();

            console.log('MCP client initialization completed successfully');
        } catch (error) {
            console.error('MCP client initialization failed:', error);
            throw error;
        }
    }

    async refreshLists() {
        try {
            // 도구 목록 가져오기
            if (this.serverCapabilities.tools) {
                const toolsResult = await this.sendRequest('tools/list');
                this.tools.clear();
                if (toolsResult.tools) {
                    for (const tool of toolsResult.tools) {
                        this.tools.set(tool.name, tool);
                    }
                }
                console.log(`Loaded ${this.tools.size} tools from MCP server`);
            }

            // 리소스 목록 가져오기
            if (this.serverCapabilities.resources) {
                const resourcesResult = await this.sendRequest('resources/list');
                this.resources.clear();
                if (resourcesResult.resources) {
                    for (const resource of resourcesResult.resources) {
                        this.resources.set(resource.uri, resource);
                    }
                }
                console.log(`Loaded ${this.resources.size} resources from MCP server`);
            }

            // 프롬프트 목록 가져오기
            if (this.serverCapabilities.prompts) {
                const promptsResult = await this.sendRequest('prompts/list');
                this.prompts.clear();
                if (promptsResult.prompts) {
                    for (const prompt of promptsResult.prompts) {
                        this.prompts.set(prompt.name, prompt);
                    }
                }
                console.log(`Loaded ${this.prompts.size} prompts from MCP server`);
            }
        } catch (error) {
            console.error('Failed to refresh MCP lists:', error);
        }
    }

    async callTool(name, args = {}) {
        if (!this.tools.has(name)) {
            throw new Error(`Tool '${name}' not found`);
        }

        const result = await this.sendRequest('tools/call', {
            name,
            arguments: args
        });

        return result;
    }

    async getResource(uri) {
        if (!this.resources.has(uri)) {
            throw new Error(`Resource '${uri}' not found`);
        }

        const result = await this.sendRequest('resources/read', {
            uri
        });

        return result;
    }

    async getPrompt(name, args = {}) {
        if (!this.prompts.has(name)) {
            throw new Error(`Prompt '${name}' not found`);
        }

        const result = await this.sendRequest('prompts/get', {
            name,
            arguments: args
        });

        return result;
    }

    getAvailableTools() {
        return Array.from(this.tools.values());
    }

    getAvailableResources() {
        return Array.from(this.resources.values());
    }

    getAvailablePrompts() {
        return Array.from(this.prompts.values());
    }

    async disconnect() {
        try {
            this.connected = false;
            
            if (this.websocket) {
                this.websocket.close();
                this.websocket = null;
            }
            
            if (this.process) {
                this.process.kill('SIGTERM');
                this.process = null;
            }
            
            this.pendingRequests.clear();
            this.emit('disconnected');
            
        } catch (error) {
            console.error('Error disconnecting MCP client:', error);
        }
    }
}

// MCP 클라이언트 매니저 - 여러 MCP 서버 관리
class MCPClientManager extends EventEmitter {
    constructor() {
        super();
        this.clients = new Map();
        this.serverConfigs = new Map();
    }

    async addServer(name, serverConfig) {
        try {
            console.log(`Adding MCP server: ${name}`);

            this.serverConfigs.set(name, serverConfig);

            const client = new MCPClient(serverConfig);
            this.clients.set(name, client);

            // 이벤트 전달
            client.on('connected', () => this.emit('server_connected', name));
            client.on('disconnected', () => this.emit('server_disconnected', name));
            client.on('error', (error) => this.emit('server_error', name, error));
            client.on('tools_changed', () => this.emit('tools_changed', name));
            client.on('resources_changed', () => this.emit('resources_changed', name));
            client.on('prompts_changed', () => this.emit('prompts_changed', name));

            // 자동 연결 (설정에 따라)
            if (serverConfig.autoConnect !== false) {
                await client.connect();
            }

            return client;
        } catch (error) {
            console.error(`Failed to add MCP server ${name}:`, error);
            throw error;
        }
    }

    async removeServer(name) {
        const client = this.clients.get(name);
        if (client) {
            await client.disconnect();
            this.clients.delete(name);
            this.serverConfigs.delete(name);
            console.log(`Removed MCP server: ${name}`);
        }
    }

    getClient(name) {
        return this.clients.get(name);
    }

    getAllClients() {
        return Array.from(this.clients.values());
    }

    getConnectedClients() {
        return Array.from(this.clients.values()).filter(client => client.connected);
    }

    async callTool(toolName, args = {}, preferredServer = null) {
        // 특정 서버 지정된 경우
        if (preferredServer) {
            const client = this.clients.get(preferredServer);
            if (client && client.connected) {
                return await client.callTool(toolName, args);
            }
        }

        // 도구를 제공하는 서버 찾기
        for (const [name, client] of this.clients.entries()) {
            if (client.connected && client.tools.has(toolName)) {
                console.log(`Using MCP server '${name}' for tool '${toolName}'`);
                return await client.callTool(toolName, args);
            }
        }

        throw new Error(`Tool '${toolName}' not found in any connected MCP server`);
    }

    getAllAvailableTools() {
        const allTools = [];
        for (const [serverName, client] of this.clients.entries()) {
            if (client.connected) {
                const tools = client.getAvailableTools();
                for (const tool of tools) {
                    allTools.push({
                        ...tool,
                        serverName
                    });
                }
            }
        }
        return allTools;
    }

    getAllAvailableResources() {
        const allResources = [];
        for (const [serverName, client] of this.clients.entries()) {
            if (client.connected) {
                const resources = client.getAvailableResources();
                for (const resource of resources) {
                    allResources.push({
                        ...resource,
                        serverName
                    });
                }
            }
        }
        return allResources;
    }

    async disconnectAll() {
        const disconnectPromises = [];
        for (const client of this.clients.values()) {
            disconnectPromises.push(client.disconnect());
        }
        await Promise.all(disconnectPromises);
        this.clients.clear();
        this.serverConfigs.clear();
    }

    getServerStatus() {
        const status = {};
        for (const [name, client] of this.clients.entries()) {
            status[name] = {
                connected: client.connected,
                toolsCount: client.tools.size,
                resourcesCount: client.resources.size,
                promptsCount: client.prompts.size,
                capabilities: client.serverCapabilities
            };
        }
        return status;
    }
}

module.exports = { MCPClient, MCPClientManager };
