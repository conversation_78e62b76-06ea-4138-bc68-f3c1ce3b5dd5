/* 전역 스타일 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
}

/* CSS 변수 (다크 테마) */
:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3d3d3d;
    --bg-hover: #4d4d4d;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #888888;
    --border-color: #404040;
    --accent-color: #007acc;
    --accent-hover: #0066aa;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --sidebar-width: 280px;
    --titlebar-height: 32px;
    --border-radius: 8px;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 라이트 테마 */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-hover: #dee2e6;
    --text-primary: #212529;
    --text-secondary: #495057;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
}

/* 타이틀바 */
.titlebar {
    height: var(--titlebar-height);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    -webkit-app-region: drag;
    user-select: none;
}

.titlebar-drag-region {
    flex: 1;
    display: flex;
    align-items: center;
    padding-left: 16px;
}

.titlebar-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-secondary);
}

.titlebar-controls {
    display: flex;
    -webkit-app-region: no-drag;
}

.titlebar-button {
    width: 46px;
    height: var(--titlebar-height);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    -webkit-app-region: no-drag; /* 드래그 영역에서 제외 */
}

.titlebar-button:hover {
    background: var(--bg-hover);
}

.titlebar-button.close:hover {
    background: var(--danger-color);
    color: white;
}

/* 메인 컨테이너 */
.app-container {
    display: flex;
    height: calc(100vh - var(--titlebar-height));
}

/* 사이드바 */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    padding: 16px;
    transition: width 0.3s ease-in-out, padding 0.3s ease-in-out;
    flex-shrink: 0;
    position: relative;
    z-index: 10;
    overflow: hidden;
}

.sidebar.collapsed {
    width: 60px; /* 축소 시 너비 */
    padding: 1px 1px; /* 좌우 패딩 축소 */
}

/* 사이드바 축소 시 텍스트 숨김 */
.sidebar.collapsed .app-title,
.sidebar.collapsed .chat-list-header span,
.sidebar.collapsed .settings-btn span,
.sidebar.collapsed .profile-btn span,
.sidebar.collapsed .chat-item-title,
.sidebar.collapsed .chat-item-preview,
.sidebar.collapsed .chat-item-delete,
.sidebar.collapsed .chat-item-time,
.sidebar.collapsed .chat-item {
    display: none;
}

/* 사이드바 축소 시 아이콘 중앙 정렬 */
.sidebar.collapsed .logo,
.sidebar.collapsed .new-chat-btn,
.sidebar.collapsed .settings-btn,
.sidebar.collapsed .profile-btn {
    justify-content: center;
}

/* 사이드바 축소 시 레이아웃 변경 */
.sidebar.collapsed .sidebar-header {
    padding: 0;
    margin-bottom: 16px;
    border-bottom: none;
    flex-direction: column; /* 세로 방향으로 변경 */
    align-items: center;
    gap: 16px;
}

.sidebar.collapsed .sidebar-toggle {
    order: -1; /* 맨 위로 이동 */
    margin-bottom: 16px;
    margin-top: 8px;
}

.sidebar.collapsed .logo {
    margin-bottom: 8px;
}

/* 사이드바 축소 시 토글 버튼 스타일 */
.sidebar.collapsed .sidebar-toggle {
    width: 30px;
    height: 30px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar.collapsed .chat-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.sidebar.collapsed .chat-list-header {
    padding: 0;
    margin-bottom: 16px;
}

.sidebar.collapsed .chat-items {
    display: none; /* 축소 시 채팅 항목 숨김 */
}

.sidebar.collapsed .sidebar-footer {
    margin-top: auto;
    padding: 16px 0;
    border-top: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

/* 사이드바 축소 시 버튼 스타일 */
.sidebar.collapsed .new-chat-btn,
.sidebar.collapsed .settings-btn,
.sidebar.collapsed .profile-btn {
    width: 25px; 
    height: 25px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar.collapsed .new-chat-btn {
    margin-bottom: 0;
}

/* 사이드바 축소 시 채팅 아이템 조정 */
.sidebar.collapsed .chat-item {
    padding: 12px 8px;
}

/* 사이드바 헤더 */
.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: bold;
    color: var(--accent-color);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1em;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.sidebar-toggle:hover {
    background: var(--bg-hover);
}

/* 채팅 목록 */
.chat-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-bottom: none; /* 혹시 있을 수 있는 하단 테두리 제거 */
}

.chat-list-header {
    padding: 10px;
}

.new-chat-btn {
    width: 100%;
    padding: 12px 16px;
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    transition: background-color 0.2s;
    margin-bottom: 16px;
}

.new-chat-btn:hover {
    background: var(--accent-hover);
}

.new-chat-btn i {
    font-size: 14px;
}

/*채팅 타이틀 제목 크기*/
.chat-items {
    flex: 1;
    overflow-y: auto;
    padding: 0 10px;
    font-size: 14px;
}

.chat-item {
    padding: 6px 12px;
    margin-bottom: 1px;
    border-radius: calc(var(--border-radius) * 1.5); 
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-item:hover {
    background: var(--bg-hover);
}

.chat-item.active {
    background: var(--bg-tertiary);
    color: white;
}

.chat-item-content {
    flex: 1;
    min-width: 0; /* 텍스트 오버플로우를 위해 */
}

.chat-item-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    flex-shrink: 0;
}

.chat-item.active .chat-item-icon {
    background: white;
    color: var(--accent-color);
}

.chat-item-title {
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 미리보기 텍스트 숨김 */
.chat-item-preview {
    display: none;
}

.chat-item-time {
    font-size: 11px;
    color: var(--text-muted);
    position: absolute;
    top: 10px;
    right: 30px;
}

.chat-item.active .chat-item-time {
    color: rgba(255, 255, 255, 0.8);
}

/* 채팅 항목 메뉴 버튼 */
.chat-item-menu {
    position: absolute;
    top: 1px;
    right: -10px;
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

.chat-item.active .chat-item-menu {
    color: white;
}

.chat-item:hover .chat-item-menu {
    opacity: 1;
}

.chat-item-menu:hover {
    background: var(--bg-tertiary);
}

/* 채팅 항목 메뉴 드롭다운 */
.chat-item-dropdown {
    position: absolute;
    top: 10px;
    right: 8px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    z-index: 100;
    min-width: 100px;
    display: none;
    padding: 4px 0;
    pointer-events: auto; /* 드롭다운 자체에 마우스 이벤트 허용 */
}

.chat-item-dropdown.show {
    display: block;
    animation: fadeIn 0.2s ease-out;
}

.dropdown-item {
    padding: 6px 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-primary);
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 12px;
}

.dropdown-item:hover {
    background: var(--bg-hover);
}

.dropdown-item i {
    font-size: 12px;
    width: 10x;
    text-align: center;
}

.dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: 4px 0;
}

/* 즐겨찾기 표시 */
.chat-item.favorite .chat-item-icon {
    background: var(--warning-color);
}

.chat-item.favorite::before {
    content: "★";
    position: absolute;
    top: 10px;
    left: -10px;
    font-size: 10px;
    color: var(--warning-color);
}

/* 사이드바 하단 */
.sidebar-footer {
    padding: 16px;
    border-top: 1px solid var(--border-color);
}

.settings-btn, .profile-btn {
    width: 100%;
    padding: 8px 16px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: var(--border-radius);
    transition: background-color 0.2s;
    margin-bottom: 8px;
}

.settings-btn:hover, .profile-btn:hover {
    background: var(--bg-hover);
}

/* 메인 컨텐츠 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

/* 환영 화면 */
.welcome-screen {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.welcome-content {
    text-align: center;
    max-width: 600px;
}

.welcome-logo {
    font-size: 64px;
    color: var(--accent-color);
    margin-bottom: 24px;
}

.welcome-content h1 {
    font-size: 32px;
    margin-bottom: 16px;
    font-weight: 300;
}

.welcome-content p {
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: 40px;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.quick-action-btn {
    padding: 20px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.quick-action-btn:hover {
    background: var(--bg-tertiary);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.quick-action-btn i {
    font-size: 24px;
    color: var(--accent-color);
}

/* 채팅 화면 */
.chat-screen {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%; /* 전체 높이 사용 */
    overflow: hidden; /* 내부 스크롤만 허용 */
}

/* 채팅 헤더 고정 */
.chat-header {
    position: sticky;
    top: 0;
    background: var(--bg-primary);
    z-index: 10;
    border-bottom: 1px solid var(--border-color);
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-title {
    flex: 1;
}

.chat-title h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.model-info {
    font-size: 12px;
    color: var(--text-muted);
    background: var(--bg-tertiary);
    padding: 2px 8px;
    border-radius: 4px;
}

.chat-actions {
    display: flex;
    gap: 8px;
}

.chat-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.chat-action-btn:hover {
    background: var(--bg-hover);
}

/* 내보내기 버튼 스타일 */
.chat-action-btn.export-btn {
    order: 1;
}

/* 지우기 버튼 스타일 */
.chat-action-btn.clear-btn {
    order: 2;
}

/* 메시지 컨테이너 */
.messages-container {
    flex: 1;
    overflow-y: auto; /* 세로 스크롤 허용 */
    padding: 0 24px;
    position: relative; /* 포지셔닝 컨텍스트 설정 */
}

.messages {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px 0;
}

.message {
    margin-bottom: 24px;
    display: flex;
    gap: 12px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: normal;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--success-color);
}

.message-content {
    flex: 1;
    max-width: 80%;
}

.message.user .message-content {
    text-align: right;
}

.message-bubble {
    /* background: var(--bg-secondary); */
    padding: 12px 16px;
    border-radius: var(--border-radius);
    margin-bottom: 4px;
    position: relative;
}

.message.user .message-bubble {
    background: var(--bg-secondary);
    color: white;
}

.message-time {
    font-size: 11px;
    color: var(--text-muted);
}

.message.user .message-time {
    text-align: right;
}

/* 입력 영역 */
.input-area {
    padding: 24px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-primary);
    position: sticky; /* 고정 위치 */
    bottom: 0; /* 하단에 고정 */
    z-index: 10; /* 다른 요소 위에 표시 */
}

.input-container {
    max-width: 800px;
    margin: 0 auto;
}

.input-wrapper {
    display: flex;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 12px;
    gap: 12px;
}

.input-wrapper:focus-within {
    border-color: var(--accent-color);
}

#message-input {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
    resize: none;
    max-height: 120px;
    min-height: 20px;
}

#message-input:focus {
    outline: none;
}

#message-input::placeholder {
    color: var(--text-muted);
}

.input-actions {
    display: flex;
    gap: 8px;
    align-items: flex-end;
}

.attach-btn, .send-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.attach-btn {
    background: none;
    color: var(--text-secondary);
}

.attach-btn:hover {
    background: var(--bg-hover);
}

.send-btn {
    background: var(--accent-color);
    color: white;
}

.send-btn:hover:not(:disabled) {
    background: var(--accent-hover);
}

.send-btn:disabled {
    background: var(--bg-hover);
    color: var(--text-muted);
    cursor: not-allowed;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: var(--text-muted);
}

/* 모달 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 20px;
    font-weight: 600;
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--bg-hover);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 설정 */
.settings-section {
    margin-bottom: 32px;
}

.settings-section h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.setting-item {
    margin-bottom: 16px;
}

.setting-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-secondary);
}

.setting-item input[type="text"],
.setting-item input[type="number"] {
    width: 100%;
    padding: 8px 12px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 14px;
}

.setting-item input[type="text"]:focus,
.setting-item input[type="number"]:focus {
    outline: none;
    border-color: var(--accent-color);
}

.setting-item input[type="range"] {
    width: 100%;
    margin-right: 12px;
}

.setting-item input[type="checkbox"] {
    display: flex;
    margin-right: 1px;
    order: -1;
}

/* 버튼 */
.btn-primary, .btn-secondary {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-hover);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-hover);
}

/* 로딩 오버레이 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.active {
    display: flex;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.fa-spinner {
    font-size: 40px;
    color: var(--accent-color);
    margin-bottom: 16px;
}

.loading-text {
    color: white;
    font-size: 16px;
    font-weight: 500;
    margin-top: 16px;
}

/* 메시지 컨테이너에 상대 위치 설정 */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 0 24px;
    position: relative; /* 로딩 오버레이의 기준점 */
}

/* 스크롤바 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-hover);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* 반응형 */
@media (max-width: 768px) {
    .sidebar {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        z-index: 100;
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        width: 100%;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .message-content {
        max-width: 85%;
    }
}

/* 애니메이션 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.message {
    animation: slideUp 0.3s ease-out;
}

.modal-content {
    animation: fadeIn 0.2s ease-out;
}

/* 텍스트 선택 */
::selection {
    background: var(--accent-color);
    color: white;
}

/* 포커스 링 제거 */
button:focus,
input:focus,
textarea:focus {
    outline: none;
}

/* 마크다운 스타일 */
.message-bubble code {
    background: var(--bg-tertiary);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 0.9em;
}

.message-bubble pre {
    background: var(--bg-tertiary);
    padding: 12px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 8px 0;
}

.message-bubble pre code {
    background: none;
    padding: 0;
}

.message-bubble blockquote {
    border-left: 3px solid var(--accent-color);
    padding-left: 12px;
    margin: 8px 0;
    color: var(--text-secondary);
}

.message-bubble ul, .message-bubble ol {
    padding-left: 20px;
    margin: 8px 0;
}

.message-bubble li {
    margin: 4px 0;
}

/* 파일 첨부 미리보기 */
.attachment-preview {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--bg-tertiary);
    padding: 8px 12px;
    border-radius: 6px;
    margin-top: 8px;
    font-size: 12px;
}

.attachment-preview i {
    color: var(--accent-color);
}

.attachment-remove {
    margin-left: auto;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
}

.attachment-remove:hover {
    color: var(--danger-color);
}

/* 상태 표시 */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-indicator.online {
    background: var(--success-color);
}

.status-indicator.offline {
    background: var(--text-muted);
}

.status-indicator.connecting {
    background: var(--warning-color);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 설정 모달 스타일 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    display: flex;
    width: 900px;
    max-width: 95%;
    height: 80vh;
    max-height: 95vh;
}

.settings-container {
    display: flex;
    width: 100%;
    height: 100%;
}

.settings-sidebar {
    width: 200px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    padding: 20px 0;
    flex-shrink: 0;
}

.settings-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.settings-nav li {
    margin: 0;
}

.settings-nav button {
    width: 100%;
    padding: 12px 20px;
    background: none;
    border: none;
    text-align: left;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.settings-nav button:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.settings-nav button.active {
    background: var(--accent-color);
    color: white;
}

.settings-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    padding-bottom: 80px;
}

.settings-section {
    display: none;
    max-width: 500px;
}

.settings-section.active {
    display: block;
}

.settings-section h2 {
    margin: 0 0 20px 0;
    font-size: 24px;
    font-weight: 600;
}

.settings-group {
    margin-bottom: 30px;
}

.settings-group h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 14px;
    box-sizing: border-box;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    border-radius: 24px;
    transition: 0.4s;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    border-radius: 50%;
    transition: 0.4s;
}

input:checked + .toggle-slider {
    background-color: var(--accent-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.mcp-server-list {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    margin-top: 10px;
    width: 100%;
}

.mcp-server-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
}

.mcp-server-item:last-child {
    border-bottom: none;
}

.mcp-server-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 500;
}

.mcp-server-info p {
    margin: 0;
    font-size: 12px;
    color: var(--text-secondary);
}

.mcp-server-actions {
    display: flex;
    gap: 10px;
}

.settings-footer {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 200px;
    padding: 20px 30px;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    box-sizing: border-box;
}

.status-online {
    background: #10b981;
}

.status-offline {
    background: #ef4444;
}

.add-server-form {
    display: none;
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 6px;
    margin-top: 15px;
}

.add-server-form.active {
    display: block;
}

/* iframe 모달 스타일 */
.modal-content {
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    width: 900px;
    max-width: 95%;
    height: 80vh;
    max-height: 95vh;
}

#settings-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: var(--bg-primary);
    flex: 1;
}

/* MCP 서버 관련 스타일 */
.mcp-server-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-primary);
}

.mcp-server-item:last-child {
    border-bottom: none;
}

.server-info {
    flex: 1;
}

.server-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.server-header strong {
    font-size: 14px;
    color: var(--text-primary);
}

.server-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.server-status.running {
    background: #10b981;
    color: white;
}

.server-status.stopped {
    background: #6b7280;
    color: white;
}

.server-status.error {
    background: #ef4444;
    color: white;
}

.server-info small {
    color: var(--text-secondary);
    font-size: 12px;
}

.server-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 11px;
}

.btn-success {
    background: #10b981;
    color: white;
    border: none;
}

.btn-success:hover:not(:disabled) {
    background: #059669;
}

.btn-success:disabled {
    background: #6b7280;
    cursor: not-allowed;
}

.allowed-directories-container {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 10px;
    background: var(--bg-secondary);
}

.directory-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    margin-bottom: 5px;
    background: var(--bg-primary);
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.directory-item:last-child {
    margin-bottom: 0;
}

.directory-path {
    flex: 1;
    font-family: monospace;
    font-size: 12px;
    color: var(--text-primary);
}

.directory-remove {
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 10px;
    cursor: pointer;
}

.directory-remove:hover {
    background: #dc2626;
}

.directory-input-group {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.directory-input-group input {
    flex: 1;
}

.empty-state {
    text-align: center;
    color: var(--text-secondary);
    padding: 20px;
    font-style: italic;
}

.server-status-container {
    max-height: 300px;
    overflow-y: auto;
}

/* 디렉토리 도움말 스타일 */
.directory-helper {
    margin: 10px 0;
}

.common-paths {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin: 10px 0;
}

.common-path-btn {
    font-family: monospace;
    font-size: 11px !important;
    padding: 4px 8px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.common-path-btn:hover {
    background: var(--accent-color) !important;
    color: white !important;
}

.default-badge {
    background: var(--accent-color);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
}

/* 서버 상태 표시 개선 */
.server-status-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin: 4px 0;
    background: var(--bg-secondary);
    border-radius: 6px;
    border-left: 3px solid transparent;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 10px;
    flex-shrink: 0;
}

.status-online {
    background: #28a745;
    box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
}

.status-offline {
    background: #6c757d;
}

.status-error {
    background: #dc3545;
    box-shadow: 0 0 4px rgba(220, 53, 69, 0.5);
}

.status-info {
    flex: 1;
    min-width: 0;
}

.status-info strong {
    display: block;
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 2px;
}

.status-info small {
    color: var(--text-secondary);
    font-size: 12px;
}

.status-command {
    font-family: monospace;
    font-size: 11px;
    color: var(--text-muted);
    margin-top: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 상태별 테두리 색상 */
.server-status-item:has(.status-online) {
    border-left-color: #28a745;
}

.server-status-item:has(.status-error) {
    border-left-color: #dc3545;
}

.server-status-item:has(.status-offline) {
    border-left-color: #6c757d;
}

/* LLM 설정 실시간 업데이트 시각적 피드백 */
:root {
    --accent-color-light: rgba(0, 123, 255, 0.1);
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group input[type="range"]:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 자동 업데이트 애니메이션 */
@keyframes highlight-update {
    0% {
        background-color: var(--accent-color-light);
        transform: scale(1.02);
    }
    50% {
        background-color: var(--accent-color-light);
        transform: scale(1.02);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

.auto-updated {
    animation: highlight-update 1s ease-in-out;
}

/* LLM 모델 선택 강조 */
#llmModelSelect {
    font-weight: 500;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

#llmModelSelect:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* 세부 모델 선택 스타일 */
#modelSelect {
    transition: all 0.3s ease;
}

#modelSelect option {
    padding: 8px;
    font-family: monospace;
}

/* 툴팁 */
[title] {
    position: relative;
}

/* 다크모드 토글 효과 */
.theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 허용 디렉토리 관련 스타일 */
.allowed-directories-container {
    margin-bottom: 15px;
}

#allowedDirectoriesList {
    margin-bottom: 10px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 5px;
}

.directory-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid var(--border-color);
}

.directory-item:last-child {
    border-bottom: none;
}

.directory-path {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
}

.directory-actions {
    display: flex;
    gap: 5px;
}

.default-badge {
    background-color: var(--accent-color);
    color: white;
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 3px;
    margin-left: 5px;
}

.directory-input-group {
    display: flex;
    gap: 5px;
    margin-bottom: 5px;
}

.directory-input-group input {
    flex: 1;
}

.empty-state {
    padding: 10px;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    font-size: 13px;
}

/* 버튼 크기 조정 */
.btn-sm {
    padding: 4px 8px;
    font-size: 11px;
}

/* 알림 스타일 추가 */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transition: all 0.3s ease;
    opacity: 1;
    transform: translateY(0);
}

.notification.success {
    background: var(--success-color);
    color: white;
}

.notification.error {
    background: var(--danger-color);
    color: white;
}

.notification.info {
    background: var(--info-color);
    color: white;
}

.notification.hide {
    opacity: 0;
    transform: translateY(20px);
}

/* 체크박스 그룹 스타일 */
.checkbox-group label {
    display: flex;
    margin-top: 20px;
    margin-bottom: 20px; /* 체크박스 그룹 내 요소 간격 조정 */
    font-size: 14px;
    align-items: center;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 1px; /* 체크박스와 레이블 간격 조정 */
    order: -1; /* 항상 첫 번째 요소로 배치 */
}

/* 메시지 로딩 인디케이터 - 왼쪽 정렬 */
.message-loading-indicator {
    display: flex;
    align-items: baseline; /* 기준선 정렬 */
    gap: 8px;
    margin-top: 0;
    margin-bottom: 20px;
    animation: fadeIn 0.3s ease-out;
    justify-content: flex-start;
    margin-left: 44px;
}

.loading-spinner {
    width: 14px;
    height: 14px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 80%;
    border-top-color: var(--accent-color);
    animation: spin 1s linear infinite;
    position: relative;
    top: 2px; /* 기준선에 맞게 조정 */
}

.loading-text {
    display: inline-flex; /* 인라인 플렉스로 설정 */
    align-items: center; /* 수직 중앙 정렬 */
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1px;
    height: 14px; /* 로딩 아이콘과 동일한 높이 */
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 안전 그래픽 모드 스타일 */
.safe-graphics-mode {
    /* 애니메이션 비활성화 */
    --transition-speed: 0s !important;
}

.safe-graphics-mode * {
    transition: none !important;
    animation: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
}

.safe-mode-indicator {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 9999;
}

.restart-normal-mode-btn {
    position: fixed;
    bottom: 10px;
    right: 150px;
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    z-index: 9999;
}

.restart-normal-mode-btn:hover {
    background-color: #45a049;
}

/* MCP 클라이언트 상태 스타일 */
.client-status-container {
    margin-top: 15px;
}

.client-status-item {
    display: flex;
    flex-direction: column;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 10px;
    background: var(--bg-primary);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.server-name {
    font-weight: 500;
    font-size: 14px;
}

.status-indicator {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-indicator.connected {
    background: var(--success-color);
    color: white;
}

.status-indicator.disconnected {
    background: var(--error-color);
    color: white;
}

.status-details {
    color: var(--text-secondary);
    font-size: 12px;
}

/* MCP 도구 목록 스타일 */
.tools-container {
    margin-top: 15px;
}

.tool-item {
    display: flex;
    flex-direction: column;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 10px;
    background: var(--bg-primary);
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.tool-name {
    font-weight: 500;
    font-size: 14px;
    color: var(--accent-color);
}

.tool-server {
    font-size: 11px;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 2px 6px;
    border-radius: 4px;
}

.tool-description {
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-size: 12px;
}

.test-tool-btn {
    align-self: flex-start;
    padding: 4px 12px;
    font-size: 11px;
}

/* Claude Desktop 호환 스타일 */
.claude-compat-container {
    margin-top: 15px;
}

.claude-compat-container .btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* 허용 디렉토리 관리 스타일 */
.allowed-directories-list {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    min-height: 100px;
    max-height: 200px;
    overflow-y: auto;
    background: var(--bg-primary);
    padding: 10px;
    margin-bottom: 10px;
}

.directory-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.directory-item:last-child {
    margin-bottom: 0;
}

.directory-path {
    flex: 1;
    font-family: monospace;
    font-size: 13px;
    color: var(--text-primary);
    word-break: break-all;
    margin-right: 10px;
}

.allowed-directories-list .empty-state {
    padding: 20px;
    margin: 0;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
}

.form-hint {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: var(--text-secondary);
}

/* MCP 클라이언트 상태 스타일 */
.client-status-container {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    min-height: 100px;
    max-height: 300px;
    overflow-y: auto;
    background: var(--bg-primary);
    padding: 15px;
    margin-bottom: 15px;
}

.client-status-item {
    padding: 12px;
    margin-bottom: 10px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
}

.client-status-item:last-child {
    margin-bottom: 0;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.server-name {
    font-weight: 600;
    color: var(--text-primary);
}

.status-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator.connected {
    background: #d4edda;
    color: #155724;
}

.status-indicator.disconnected {
    background: #f8d7da;
    color: #721c24;
}

.status-details {
    font-size: 13px;
    color: var(--text-secondary);
}

/* MCP 도구 목록 스타일 */
.tools-container {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    min-height: 150px;
    max-height: 400px;
    overflow-y: auto;
    background: var(--bg-primary);
    padding: 15px;
    margin-bottom: 15px;
}

.tool-item {
    padding: 15px;
    margin-bottom: 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
}

.tool-item:last-child {
    margin-bottom: 0;
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.tool-name {
    font-weight: 600;
    color: var(--text-primary);
    font-family: monospace;
}

.tool-server {
    padding: 2px 6px;
    background: var(--accent-color);
    color: white;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
}

.tool-description {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 10px;
    line-height: 1.4;
}

.test-tool-btn {
    font-size: 12px;
    padding: 6px 12px;
}

/* MCP 진단 결과 스타일 */
.diagnosis-results {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
}

.diagnosis-results h4 {
    margin: 0 0 15px 0;
    color: var(--text-primary);
    font-size: 16px;
}

.diagnosis-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.diagnosis-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.diagnosis-section h5 {
    margin: 0 0 10px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.diagnosis-item {
    padding: 8px 12px;
    margin-bottom: 8px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.4;
}

.diagnosis-item:last-child {
    margin-bottom: 0;
}

.diagnosis-warning {
    color: #f39c12;
    font-style: italic;
    margin: 10px 0;
}

.diagnosis-error {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    padding: 8px 12px;
    border-radius: 4px;
    margin-bottom: 8px;
    font-size: 13px;
}
