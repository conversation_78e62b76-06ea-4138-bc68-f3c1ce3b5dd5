const { app, <PERSON><PERSON>erWindow, <PERSON>u, ipc<PERSON>ain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const fsSync = require('fs');
const { spawn } = require('child_process');
const ConfigManager = require('./config-manager');
const { MCPClientManager } = require('./mcp-client');

// 애플리케이션 시작 시 가장 먼저 실행되는 부분에 추가
// 다른 require 문 위에 배치
app.commandLine.appendSwitch('disable-gpu');
app.commandLine.appendSwitch('disable-gpu-compositing');
app.commandLine.appendSwitch('disable-gpu-rasterization');
app.commandLine.appendSwitch('disable-software-rasterizer');
app.commandLine.appendSwitch('disable-accelerated-2d-canvas');
app.commandLine.appendSwitch('disable-accelerated-video-decode');
app.disableHardwareAcceleration();

// 그래픽 메모리 사용량 제한
app.commandLine.appendSwitch('gpu-memory-buffer-pool-size', '64');
app.commandLine.appendSwitch('num-raster-threads', '2');

// GPU 프로세스 충돌 감지 및 처리
let gpuCrashCount = 0;
const MAX_GPU_CRASHES = 3;
const GPU_CRASH_RESET_INTERVAL = 60000; // 1분

// GPU 충돌 카운터 리셋 타이머
setInterval(() => {
    if (gpuCrashCount > 0) {
        console.log(`Resetting GPU crash counter from ${gpuCrashCount} to 0`);
        gpuCrashCount = 0;
    }
}, GPU_CRASH_RESET_INTERVAL);

// GPU 프로세스 충돌 이벤트 핸들러
app.on('child-process-gone', (event, details) => {
    if (details.type === 'gpu') {
        gpuCrashCount++;
        console.error(`GPU process crashed (${gpuCrashCount}/${MAX_GPU_CRASHES}):`, details);
        
        if (gpuCrashCount >= MAX_GPU_CRASHES) {
            console.error(`GPU crashed ${gpuCrashCount} times. Restarting with safe graphics mode...`);
            
            // 안전 모드 플래그 설정
            app.relaunch({ args: process.argv.slice(1).concat(['--safe-graphics-mode']) });
            app.exit(0);
        }
    }
});

// 안전 모드 감지
const isSafeGraphicsMode = process.argv.includes('--safe-graphics-mode');
if (isSafeGraphicsMode) {
    console.log('Starting in safe graphics mode with minimal GPU features');
    // 추가 GPU 비활성화 옵션
    app.commandLine.appendSwitch('disable-reading-from-canvas');
    app.commandLine.appendSwitch('disable-accelerated-video');
    app.commandLine.appendSwitch('disable-d3d11');
    app.commandLine.appendSwitch('disable-webgl');
    app.commandLine.appendSwitch('disable-webgl2');
}

let mainWindow;
let settingsWindow;
const mcpServerProcesses = new Map(); // MCP 서버 프로세스들
let configManager = new ConfigManager();

// 전역 MCP 서버 프로세스 맵 설정 (MCP 클라이언트에서 접근 가능)
global.mcpServerProcesses = mcpServerProcesses;

// MCP 클라이언트 매니저 초기화
const mcpClientManager = new MCPClientManager();

// 개발 모드 확인
const isDev = process.env.NODE_ENV === 'development';

// 앱 설정
const APP_CONFIG = {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js')
    }
};

// 메인 윈도우 생성
function createWindow() {
    // 안전 모드에서는 창 크기 축소 및 효과 최소화
    const windowOptions = {
        width: isSafeGraphicsMode ? 1000 : 1200,
        height: isSafeGraphicsMode ? 700 : 800,
        minWidth: 800,
        minHeight: 600,
        show: false,
        backgroundColor: '#ffffff', // 배경색 설정으로 깜빡임 방지
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js'),
            // GPU 관련 설정
            offscreen: false,
            backgroundThrottling: false
        }
    };

    // 안전 모드에서는 프레임 사용
    if (isSafeGraphicsMode) {
        windowOptions.frame = true;
        windowOptions.titleBarStyle = 'default';
    } else {
        windowOptions.icon = path.join(__dirname, 'assets', 'icon.png');
        windowOptions.titleBarStyle = process.platform === 'darwin' ? 'hiddenInset' : 'hidden';
        windowOptions.frame = process.platform === 'darwin';
    }

    mainWindow = new BrowserWindow(windowOptions);

    // 안전 모드 표시
    if (isSafeGraphicsMode) {
        mainWindow.setTitle('MCP Desktop Clone - 안전 그래픽 모드');
    }

    // HTML 파일 로드
    mainWindow.loadFile('index.html');

    // 개발 모드에서 DevTools 열기
    if (isDev && !isSafeGraphicsMode) {
        mainWindow.webContents.openDevTools();
    }

    // 윈도우가 준비되면 표시
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // 안전 모드 알림
        if (isSafeGraphicsMode) {
            dialog.showMessageBox(mainWindow, {
                type: 'warning',
                title: '안전 그래픽 모드',
                message: '그래픽 가속 문제로 인해 안전 모드로 실행됩니다.',
                detail: '애플리케이션이 하드웨어 가속 없이 실행 중입니다. 일부 시각적 효과와 성능이 제한될 수 있습니다.',
                buttons: ['확인']
            });
        }
    });

    // 창 닫기 이벤트 처리
    mainWindow.on('close', (e) => {
        console.log('Window close event triggered');

        // 이미 종료 중이면 바로 종료
        if (app.isQuiting) {
            console.log('App is already quitting, allowing close');
            return;
        }

        // 비동기 작업을 위해 이벤트 취소
        e.preventDefault();

        // 안전하게 렌더러에 창 닫기 이벤트 전송
        try {
            if (mainWindow && mainWindow.webContents && !mainWindow.isDestroyed()) {
                console.log('Sending before-close event to renderer');
                mainWindow.webContents.send('before-close');
            }
        } catch (error) {
            console.error('Error sending before-close event:', error);
        }

        // 리소스 정리 및 앱 종료
        console.log('Starting cleanup and quit process');
        app.isQuiting = true;

        // MCP 서버 정리
        stopAllMcpServers();

        // 약간의 지연 후 앱 종료 (상태 저장 시간 확보)
        setTimeout(() => {
            try {
                if (mainWindow && !mainWindow.isDestroyed()) {
                    mainWindow.destroy();
                }
            } catch (error) {
                console.error('Error destroying main window:', error);
            }
            mainWindow = null;
            app.quit();
        }, 100);
    });

    // 외부 링크 처리
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}

// 메뉴 생성
function createMenu() {
    const template = [
        {
            label: 'File',
            submenu: [
                {
                    label: 'New Chat',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.webContents.send('new-chat');
                    }
                },
                {
                    label: 'Export Chat',
                    accelerator: 'CmdOrCtrl+E',
                    click: () => {
                        mainWindow.webContents.send('export-chat');
                    }
                },
                { type: 'separator' },
                {
                    label: 'Settings',
                    accelerator: 'CmdOrCtrl+,',
                    click: () => {
                        mainWindow.webContents.send('open-settings');
                    }
                },
                { type: 'separator' },
                {
                    label: 'Quit',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'Edit',
            submenu: [
                { role: 'undo' },
                { role: 'redo' },
                { type: 'separator' },
                { role: 'cut' },
                { role: 'copy' },
                { role: 'paste' },
                { role: 'selectAll' }
            ]
        },
        {
            label: 'View',
            submenu: [
                { role: 'reload' },
                { role: 'forceReload' },
                { role: 'toggleDevTools' },
                { type: 'separator' },
                { role: 'resetZoom' },
                { role: 'zoomIn' },
                { role: 'zoomOut' },
                { type: 'separator' },
                { role: 'togglefullscreen' }
            ]
        },
        {
            label: 'Window',
            submenu: [
                { role: 'minimize' },
                { role: 'close' },
                {
                    label: 'Toggle Sidebar',
                    accelerator: 'CmdOrCtrl+\\',
                    click: () => {
                        mainWindow.webContents.send('toggle-sidebar');
                    }
                }
            ]
        },
        {
            label: 'Help',
            submenu: [
                {
                    label: 'About MCP Desktop Clone',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'About MCP Desktop Clone',
                            message: 'MCP Desktop Clone',
                            detail: 'Claude Desktop 호환 AI 어시스턴트\nLlama4 Maverick 기반\n\nVersion: 1.0.0'
                        });
                    }
                }
            ]
        }
    ];

    // macOS 메뉴 조정
    if (process.platform === 'darwin') {
        template.unshift({
            label: app.getName(),
            submenu: [
                { role: 'about' },
                { type: 'separator' },
                { role: 'services' },
                { type: 'separator' },
                { role: 'hide' },
                { role: 'hideOthers' },
                { role: 'unhide' },
                { type: 'separator' },
                { role: 'quit' }
            ]
        });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// MCP 서버 관리
// 이미 선언된 변수들:
// let mainWindow;
// const mcpServerProcesses = new Map();
// let configManager = new ConfigManager();

// MCP 서버 상태 변경 이벤트 처리 함수 개선
function sendServerStatusUpdate(name, running) {
    try {
        // mainWindow가 존재하고 webContents가 유효한지 확인
        if (mainWindow && mainWindow.webContents && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('mcp:server-status-changed', { 
                name, 
                running 
            });
        } else {
            console.log(`Cannot send server status update: mainWindow is ${mainWindow ? 'not ready' : 'null'}`);
        }
    } catch (error) {
        console.error('Error sending server status update:', error);
    }
}

// MCP 서버 시작 핸들러 개선
ipcMain.handle('mcp:start-server-with-config', async (_, config) => {
    try {
        const { name, command, args = [], env = {}, internal = false } = config;

        // 모든 서버를 외부 프로세스로 처리 (Claude Desktop 표준)
        // 경로에 공백이 있는 경우 따옴표로 감싸기
        const processedArgs = args.map(arg => {
            // 경로처럼 보이는 인수에 공백이 있으면 따옴표로 감싸기
            if (arg.includes('\\') || arg.includes('/')) {
                if (arg.includes(' ') && !arg.startsWith('"') && !arg.endsWith('"')) {
                    return `"${arg}"`;
                }
            }
            return arg;
        });

        if (mcpServerProcesses.has(name)) {
            return {
                success: false,
                error: `Server ${name} is already running`
            };
        }

        console.log(`Starting external MCP server: ${name}`);
        console.log(`Command: ${command}`);
        console.log(`Args: ${processedArgs.join(' ')}`);

        const childProcess = spawn(command, processedArgs, {
            env: { ...process.env, ...env },
            shell: true // Windows 호환성을 위해 쉘 사용
        });

        mcpServerProcesses.set(name, childProcess);

        childProcess.on('exit', (code, signal) => {
            console.log(`MCP Server ${name} exited with code ${code}, signal ${signal}`);
            mcpServerProcesses.delete(name);
            sendServerStatusUpdate(name, false);
        });

        childProcess.on('error', (error) => {
            console.error(`MCP Server ${name} error:`, error);
            mcpServerProcesses.delete(name);
            sendServerStatusUpdate(name, false);
        });

        // 표준 출력 처리
        childProcess.stdout.on('data', (data) => {
            console.log(`[${name}] ${data.toString().trim()}`);
        });

        childProcess.stderr.on('data', (data) => {
            console.error(`[${name}] ${data.toString().trim()}`);
        });

        // 서버 상태 업데이트
        sendServerStatusUpdate(name, true);

        return {
            success: true,
            message: `External server ${name} started successfully`,
            pid: childProcess.pid
        };
    } catch (error) {
        console.error('Failed to start MCP server:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

// MCP 서버 중지 핸들러
ipcMain.handle('mcp:stop-server', async (_, name) => {
    try {
        // 외부 서버 확인
        const childProcess = mcpServerProcesses.get(name);
        if (!childProcess) {
            return {
                success: false,
                error: `Server ${name} is not running`
            };
        }

        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                try {
                    childProcess.kill('SIGKILL');
                } catch (e) {
                    console.error(`Failed to force kill ${name}:`, e);
                }
            }, 5000);

            childProcess.on('exit', () => {
                clearTimeout(timeout);
                mcpServerProcesses.delete(name);
                sendServerStatusUpdate(name, false);
                resolve({
                    success: true,
                    message: `Server ${name} stopped successfully`
                });
            });

            try {
                childProcess.kill('SIGTERM');
            } catch (error) {
                clearTimeout(timeout);
                console.error(`Failed to stop server ${name}:`, error);
                resolve({
                    success: false,
                    error: error.message
                });
            }
        });
    } catch (error) {
        console.error('Failed to stop MCP server:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

// MCP 서버 상태 조회 핸들러
ipcMain.handle('mcp:get-server-status', async () => {
    const status = {};

    // 모든 서버 상태 확인 (외부 프로세스)
    for (const [name, process] of mcpServerProcesses.entries()) {
        status[name] = {
            running: !process.killed && process.exitCode === null,
            pid: process.pid
        };
    }

    return status;
});

// MCP 도구 실행 함수 (LLM 타입별 최적화)
async function executeMcpTool(tool, args, llmConfig) {
    try {
        console.log(`Executing MCP tool: ${tool} with LLM type: ${llmConfig.apiType}`);

        // MCP 클라이언트를 통한 도구 호출
        try {
            console.log(`Calling MCP tool via client: ${tool}`);
            const result = await mcpClientManager.callTool(tool, args);

            if (result) {
                console.log(`MCP tool ${tool} executed successfully via client`);

                // LLM 타입에 따라 응답 형식 조정
                let formattedResult = result;
                if (typeof formattedResult === 'object') {
                    formattedResult = JSON.stringify(formattedResult, null, 2);
                }

                return {
                    success: true,
                    data: formattedResult,
                    source: 'mcp_client',
                    llmType: llmConfig.apiType
                };
            }
        } catch (mcpError) {
            console.warn(`MCP client failed for ${tool}:`, mcpError);
        }

        return { success: false, error: 'No available MCP servers' };

    } catch (error) {
        console.error(`Error executing MCP tool ${tool}:`, error);
        return { success: false, error: error.message };
    }
}

// MCP 클라이언트 관련 IPC 핸들러들
ipcMain.handle('mcp:client:connect-server', async (_, name, serverConfig) => {
    try {
        console.log(`🔌 Connecting to MCP server: ${name}`);
        console.log(`📋 Server config:`, serverConfig);

        const client = await mcpClientManager.addServer(name, serverConfig);
        console.log(`✅ MCP client connected: ${client.connected}`);

        return { success: true, connected: client.connected };
    } catch (error) {
        console.error(`❌ Failed to connect to MCP server ${name}:`, error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('mcp:client:disconnect-server', async (_, name) => {
    try {
        console.log(`Disconnecting MCP server: ${name}`);
        await mcpClientManager.removeServer(name);
        return { success: true };
    } catch (error) {
        console.error(`Failed to disconnect MCP server ${name}:`, error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('mcp:client:get-tools', async () => {
    try {
        const tools = mcpClientManager.getAllAvailableTools();
        return { success: true, tools };
    } catch (error) {
        console.error('Failed to get MCP tools:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('mcp:client:call-tool', async (_, toolName, args) => {
    try {
        console.log(`Calling MCP tool via client: ${toolName}`, args);
        const result = await mcpClientManager.callTool(toolName, args);
        return { success: true, result };
    } catch (error) {
        console.error(`Failed to call MCP tool ${toolName}:`, error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('mcp:client:get-status', async () => {
    try {
        const status = mcpClientManager.getServerStatus();
        console.log('📊 MCP Client Status Report:');
        console.log('- Total clients:', mcpClientManager.clients.size);
        console.log('- Connected clients:', mcpClientManager.getConnectedClients().length);

        for (const [name, clientStatus] of Object.entries(status)) {
            console.log(`- ${name}:`, {
                connected: clientStatus.connected,
                tools: clientStatus.toolsCount,
                resources: clientStatus.resourcesCount,
                prompts: clientStatus.promptsCount
            });
        }

        return { success: true, status };
    } catch (error) {
        console.error('❌ Failed to get MCP client status:', error);
        return { success: false, error: error.message };
    }
});

// MCP 연결 진단 핸들러 추가
ipcMain.handle('mcp:diagnose', async () => {
    try {
        console.log('🔍 Starting MCP connection diagnosis...');

        const diagnosis = {
            serverProcesses: {},
            clientConnections: {},
            availableTools: {},
            errors: []
        };

        // 1. 서버 프로세스 상태 확인
        console.log('📋 Checking server processes...');
        for (const [name, process] of mcpServerProcesses.entries()) {
            diagnosis.serverProcesses[name] = {
                running: !process.killed && process.exitCode === null,
                pid: process.pid,
                exitCode: process.exitCode,
                killed: process.killed
            };
            console.log(`- ${name}: PID ${process.pid}, running: ${!process.killed && process.exitCode === null}`);
        }

        // 2. 클라이언트 연결 상태 확인
        console.log('🔗 Checking client connections...');
        const clientStatus = mcpClientManager.getServerStatus();
        for (const [name, status] of Object.entries(clientStatus)) {
            diagnosis.clientConnections[name] = status;
            console.log(`- ${name}: connected: ${status.connected}, tools: ${status.toolsCount}`);
        }

        // 3. 사용 가능한 도구 확인
        console.log('🔧 Checking available tools...');
        try {
            const allTools = mcpClientManager.getAllAvailableTools();
            diagnosis.availableTools = allTools.reduce((acc, tool) => {
                if (!acc[tool.serverName]) acc[tool.serverName] = [];
                acc[tool.serverName].push(tool.name);
                return acc;
            }, {});
            console.log('- Total tools available:', allTools.length);
            for (const [serverName, tools] of Object.entries(diagnosis.availableTools)) {
                console.log(`- ${serverName}: ${tools.join(', ')}`);
            }
        } catch (toolError) {
            diagnosis.errors.push(`Failed to get tools: ${toolError.message}`);
            console.error('❌ Failed to get available tools:', toolError);
        }

        // 4. 설정 파일 확인
        console.log('📄 Checking configuration...');
        try {
            const mcpConfig = await configManager.getMcpServers();
            diagnosis.configuredServers = Object.keys(mcpConfig);
            console.log('- Configured servers:', diagnosis.configuredServers);
        } catch (configError) {
            diagnosis.errors.push(`Failed to load config: ${configError.message}`);
            console.error('❌ Failed to load MCP config:', configError);
        }

        console.log('✅ MCP diagnosis completed');
        return { success: true, diagnosis };

    } catch (error) {
        console.error('❌ MCP diagnosis failed:', error);
        return { success: false, error: error.message };
    }
});

// MCP 서버 자동 시작 함수 (내장 및 외부 서버 모두 지원)
async function startMcpServers() {
    try {
        console.log('Starting MCP servers...');

        // mcp_desktop_config.json에서 서버 목록 가져오기
        let mcpServerConfigs = {};

        try {
            // ConfigManager를 통해 MCP 설정 로드
            console.log('Loading MCP servers via ConfigManager...');
            mcpServerConfigs = await configManager.getMcpServers();
            console.log('Loaded MCP servers from mcp_desktop_config.json:', Object.keys(mcpServerConfigs));
        } catch (configError) {
            console.error('Failed to load MCP server configs:', configError);
            // 기본 외부 서버 설정 (Claude Desktop 표준)
            mcpServerConfigs = {
                'filesystem': {
                    command: 'npx',
                    args: [
                        '-y',
                        '@modelcontextprotocol/server-filesystem',
                        require('path').join(require('os').homedir(), 'Documents'),
                        require('path').join(require('os').homedir(), 'Downloads')
                    ]
                }
            };
            console.log('Using default filesystem MCP server config');
        }

        for (const [name, serverConfig] of Object.entries(mcpServerConfigs)) {
            try {
                // 비활성화된 서버는 건너뛰기
                if (serverConfig.enabled === false) {
                    console.log(`Skipping disabled server: ${name}`);
                    continue;
                }

                console.log(`Starting MCP server: ${name}`);
                const { command, args = [], env = {} } = serverConfig;

                // 모든 서버를 외부 프로세스로 처리 (Claude Desktop 표준)
                console.log(`Starting MCP server: ${name}`);

                // 경로에 공백이 있는 경우 따옴표로 감싸기
                const processedArgs = args.map(arg => {
                    if (typeof arg === 'string' && (arg.includes('\\') || arg.includes('/')) && arg.includes(' ')) {
                        return `"${arg}"`;
                    }
                    return arg;
                });

                console.log(`Executing: ${command} ${processedArgs.join(' ')}`);

                const childProcess = spawn(command, processedArgs, {
                    env: { ...process.env, ...env },
                    shell: true,
                    stdio: ['pipe', 'pipe', 'pipe']
                });

                mcpServerProcesses.set(name, childProcess);

                childProcess.on('exit', (code, signal) => {
                    console.log(`MCP Server ${name} exited with code ${code}, signal ${signal}`);
                    mcpServerProcesses.delete(name);
                });

                childProcess.on('error', (error) => {
                    console.error(`MCP Server ${name} error:`, error);
                    mcpServerProcesses.delete(name);
                });

                childProcess.stdout.on('data', (data) => {
                    console.log(`[${name}] ${data.toString().trim()}`);
                });

                childProcess.stderr.on('data', (data) => {
                    console.error(`[${name}] ${data.toString().trim()}`);
                });

                console.log(`MCP server ${name} started with PID: ${childProcess.pid}`);

                // MCP 클라이언트 연결 시도 (stdio 연결)
                try {
                    console.log(`🔗 Attempting to connect MCP client to server: ${name}`);
                    console.log(`📋 Using server config:`, serverConfig);

                    // 서버가 시작될 시간을 주기 위해 잠시 대기
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    // 서버 프로세스를 클라이언트 설정에 전달
                    const clientConfig = {
                        ...serverConfig,
                        serverName: name,
                        process: childProcess // 프로세스 직접 전달
                    };

                    const client = await mcpClientManager.addServer(name, clientConfig);
                    console.log(`✅ MCP client connected to server: ${name}`);
                    console.log(`🔧 Available tools: ${client.tools.size}`);

                    // 도구 목록 로깅
                    if (client.tools.size > 0) {
                        console.log(`📋 Tools available from ${name}:`, Array.from(client.tools.keys()));

                        // 도구 테스트 (파일시스템 서버인 경우)
                        if (name === 'filesystem') {
                            try {
                                console.log('🧪 Testing filesystem tools...');
                                const testResult = await client.callTool('read_file', { path: __filename });
                                console.log('✅ Filesystem tool test successful');
                            } catch (testError) {
                                console.warn('⚠️ Filesystem tool test failed:', testError.message);
                            }
                        }
                    } else {
                        console.warn(`⚠️ No tools available from ${name} - this may indicate a connection issue`);

                        // 연결 재시도
                        console.log(`🔄 Retrying connection to ${name}...`);
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        try {
                            await client.refreshLists();
                            console.log(`🔧 After retry - Available tools: ${client.tools.size}`);
                            if (client.tools.size > 0) {
                                console.log(`📋 Tools after retry from ${name}:`, Array.from(client.tools.keys()));
                            }
                        } catch (retryError) {
                            console.error(`❌ Retry failed for ${name}:`, retryError.message);
                        }
                    }
                } catch (clientError) {
                    console.error(`❌ Failed to connect MCP client to ${name}:`, clientError);
                    console.error(`🔍 Error details:`, {
                        message: clientError.message,
                        stack: clientError.stack
                    });
                }
            } catch (error) {
                console.error(`Failed to auto-start MCP server ${name}:`, error);
            }
        }

        console.log('MCP servers startup completed');
    } catch (error) {
        console.error('Error starting internal MCP servers:', error);
    }
}

// 모든 MCP 서버 중지 함수
function stopAllMcpServers() {
    try {
        // 모든 MCP 서버 프로세스 중지
        for (const [name, process] of mcpServerProcesses.entries()) {
            try {
                if (process && !process.killed) {
                    process.kill('SIGTERM');
                    console.log(`Stopped MCP server: ${name}`);
                }
            } catch (error) {
                console.error(`Failed to stop MCP server ${name}:`, error);
            }
        }
        mcpServerProcesses.clear();

        // MCP 클라이언트 정리
        try {
            mcpClientManager.disconnectAll();
            console.log('All MCP clients disconnected');
        } catch (error) {
            console.error('Error disconnecting MCP clients:', error);
        }
    } catch (error) {
        console.error('Error stopping all MCP servers:', error);
    }
}

// 앱 종료 시 모든 MCP 서버 중지 및 리소스 정리
app.on('before-quit', (event) => {
    console.log('Application is quitting, cleaning up resources...');

    // 이미 정리 중이면 바로 종료
    if (app.isQuiting) {
        console.log('Already quitting, skipping cleanup');
        return;
    }

    // 정리 중 표시
    app.isQuiting = true;

    // MCP 서버 정리
    stopAllMcpServers();

    console.log('Cleanup completed');
});

// IPC 핸들러들 확인 및 수정
ipcMain.handle('window-minimize', () => {
    console.log('IPC: window-minimize called');
    if (mainWindow) {
        mainWindow.minimize();
        console.log('Window minimized');
        return true;
    }
    console.log('Window not available for minimize');
    return false;
});

ipcMain.handle('window-maximize', () => {
    console.log('IPC: window-maximize called');
    if (mainWindow) {
        if (mainWindow.isMaximized()) {
            mainWindow.unmaximize();
            console.log('Window unmaximized');
        } else {
            mainWindow.maximize();
            console.log('Window maximized');
        }
        return true;
    }
    console.log('Window not available for maximize/unmaximize');
    return false;
});

ipcMain.handle('window-close', () => {
    console.log('IPC: window-close called');
    try {
        if (mainWindow && !mainWindow.isDestroyed()) {
            console.log('Closing main window via IPC');
            mainWindow.close();
            return true;
        } else {
            console.log('Window not available for close - window is null or destroyed');
            return false;
        }
    } catch (error) {
        console.error('Error in window-close handler:', error);
        return false;
    }
});

ipcMain.handle('show-save-dialog', async (event, options) => {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
});

ipcMain.handle('show-open-dialog', async (_, options) => {
    try {
        console.log('Received show-open-dialog request with options:', options);
        
        // 옵션이 없으면 기본값 설정
        const dialogOptions = options || {
            properties: ['openDirectory'],
            title: '디렉토리 선택'
        };
        
        // 대화상자 열기
        console.log('Opening dialog with options:', dialogOptions);
        const result = await dialog.showOpenDialog(mainWindow, dialogOptions);
        
        console.log('Dialog result:', result);
        return result;
    } catch (error) {
        console.error('Error showing open dialog:', error);
        return { 
            canceled: true, 
            filePaths: [],
            error: error.message 
        };
    }
});

ipcMain.handle('write-file', async (event, filePath, data) => {
    try {
        await fs.writeFile(filePath, data, 'utf8');
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

ipcMain.handle('read-file', async (event, filePath) => {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        return { success: true, data };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

// 설정 관련 핸들러 추가
ipcMain.handle('get-config', async () => {
    try {
        console.log('IPC: get-config called');
        const config = await configManager.loadConfig();
        console.log('IPC: get-config returning config');
        return config;
    } catch (error) {
        console.error('IPC: Failed to get config:', error);
        // 오류 발생 시 기본 설정 반환
        const defaultConfig = configManager.getDefaultConfig();
        console.log('IPC: Returning default config due to error');
        return defaultConfig;
    }
});

ipcMain.handle('save-config', async (event, config) => {
    try {
        console.log('IPC: save-config called');
        if (!config || typeof config !== 'object') {
            console.error('IPC: Invalid config object received');
            return { success: false, error: '유효하지 않은 설정 객체입니다.' };
        }
        
        await configManager.saveConfig(config);
        console.log('IPC: Config saved successfully');
        return { success: true };
    } catch (error) {
        console.error('IPC: Failed to save config:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('close-settings', () => {
    console.log('IPC: close-settings called');
    
    try {
        // 모든 창 가져오기
        const allWindows = BrowserWindow.getAllWindows();
        
        // 설정 창 찾기 (URL로 식별)
        const settingsWindow = allWindows.find(win => {
            try {
                const url = win.webContents.getURL();
                return url.includes('settings.html');
            } catch (e) {
                return false;
            }
        });
        
        if (settingsWindow) {
            console.log('Found settings window, closing it');
            settingsWindow.close();
            return { success: true };
        } else if (mainWindow) {
            console.log('Settings window not found, sending close-settings to main window');
            mainWindow.webContents.send('close-settings');
            return { success: true };
        }
        
        console.log('No windows found to close settings');
        return { success: false, error: '설정 창을 찾을 수 없습니다.' };
    } catch (error) {
        console.error('Error in close-settings handler:', error);
        return { success: false, error: error.message };
    }
});

// 모든 IPC 이벤트에서 안전하게 메시지 전송하는 헬퍼 함수
function safelySendToRenderer(channel, ...args) {
    try {
        if (mainWindow && mainWindow.webContents && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send(channel, ...args);
            return true;
        } else {
            console.log(`Cannot send message to renderer: mainWindow is ${mainWindow ? 'not ready' : 'null'}`);
            return false;
        }
    } catch (error) {
        console.error(`Error sending message to renderer (${channel}):`, error);
        return false;
    }
}

// 언어 변경 처리 핸들러 개선
ipcMain.handle('change-language', async (event, language) => {
    try {
        console.log(`Changing language to: ${language}`);
        
        // 설정에 언어 저장
        const config = await configManager.loadConfig();
        config.language = language;
        await configManager.saveConfig(config);
        
        // 언어 변경 이벤트를 모든 창에 전송
        BrowserWindow.getAllWindows().forEach(win => {
            try {
                if (win && win.webContents && !win.isDestroyed()) {
                    console.log(`Sending language-changed event to window ${win.id}`);
                    win.webContents.send('language-changed', language);
                }
            } catch (error) {
                console.error(`Failed to send language-changed event to window ${win.id}:`, error);
            }
        });

        return { success: true };
    } catch (error) {
        console.error('Failed to change language:', error);
        return { success: false, error: error.message };
    }
});

// 설정 초기화 핸들러
ipcMain.handle('reset-config', async () => {
    try {
        console.log('IPC: reset-config called');
        const defaultConfig = configManager.getDefaultConfig();
        console.log('IPC: Got default config, saving...');
        await configManager.saveConfig(defaultConfig);
        console.log('IPC: Config reset successfully');
        return { success: true };
    } catch (error) {
        console.error('IPC: Failed to reset config:', error);
        return { success: false, error: error.message };
    }
});

// API 키 저장 핸들러
ipcMain.handle('save-api-key', async (event, apiKey) => {
    try {
        console.log('IPC: save-api-key called');
        if (typeof apiKey !== 'string') {
            console.error('IPC: Invalid API key type:', typeof apiKey);
            return { success: false, error: '유효하지 않은 API 키 형식입니다.' };
        }
        
        await configManager.saveApiKey(apiKey);
        console.log('IPC: API key saved successfully');
        return { success: true };
    } catch (error) {
        console.error('IPC: Failed to save API key:', error);
        return { success: false, error: error.message };
    }
});

// API 키 가져오기 핸들러
ipcMain.handle('get-api-key', async () => {
    try {
        console.log('IPC: get-api-key called');
        const apiKey = configManager.getApiKey();
        console.log('IPC: API key retrieved successfully:', !!apiKey);
        return { success: true, apiKey };
    } catch (error) {
        console.error('IPC: Failed to get API key:', error);
        return { success: false, error: error.message };
    }
});

// API 키 직접 저장 핸들러 (디버깅용)
ipcMain.handle('save-api-key-direct', async (event, apiKey) => {
    try {
        console.log('IPC: save-api-key-direct called');
        if (typeof apiKey !== 'string') {
            console.error('IPC: Invalid API key type:', typeof apiKey);
            return { success: false, error: '유효하지 않은 API 키 형식입니다.' };
        }
        
        // 직접 설정 객체에 저장
        if (!configManager.config) {
            await configManager.loadConfig();
        }
        
        if (!configManager.config.llmConfig) {
            configManager.config.llmConfig = {};
        }
        
        configManager.config.llmConfig.directApiKey = apiKey;
        await configManager.saveConfig(configManager.config);
        
        console.log('IPC: API key saved directly successfully');
        return { success: true };
    } catch (error) {
        console.error('IPC: Failed to save API key directly:', error);
        return { success: false, error: error.message };
    }
});

// MCP 설정 저장 핸들러
ipcMain.handle('save-mcp-config', async (_, mcpConfig) => {
    try {
        console.log('IPC: save-mcp-config called with:', mcpConfig);
        await configManager.saveMcpConfig(mcpConfig);
        console.log('IPC: MCP config saved successfully to mcp_desktop_config.json');
        return { success: true };
    } catch (error) {
        console.error('IPC: Failed to save MCP config:', error);
        return { success: false, error: error.message };
    }
});

// MCP 서버 설정 관련 핸들러들
ipcMain.handle('mcp:add-server', async (_, name, serverConfig) => {
    try {
        console.log(`Adding MCP server: ${name}`, serverConfig);
        const result = await configManager.addMcpServer(name, serverConfig);
        return { success: true, result };
    } catch (error) {
        console.error(`Failed to add MCP server ${name}:`, error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('mcp:remove-server', async (_, name) => {
    try {
        console.log(`Removing MCP server: ${name}`);
        const result = await configManager.removeMcpServer(name);
        return { success: true, result };
    } catch (error) {
        console.error(`Failed to remove MCP server ${name}:`, error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('mcp:get-servers', async () => {
    try {
        const servers = await configManager.getMcpServers();
        return { success: true, servers };
    } catch (error) {
        console.error('Failed to get MCP servers:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('mcp:get-server-templates', async () => {
    try {
        const templates = configManager.getDefaultMcpServerTemplates();
        return { success: true, templates };
    } catch (error) {
        console.error('Failed to get MCP server templates:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('mcp:export-claude-config', async () => {
    try {
        const claudeConfig = configManager.exportClaudeDesktopConfig();
        return { success: true, config: claudeConfig };
    } catch (error) {
        console.error('Failed to export Claude Desktop config:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('mcp:import-claude-config', async (_, claudeConfig) => {
    try {
        console.log('Importing Claude Desktop config:', claudeConfig);
        const result = await configManager.importClaudeDesktopConfig(claudeConfig);
        return { success: true, result };
    } catch (error) {
        console.error('Failed to import Claude Desktop config:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('mcp:validate-config', async () => {
    try {
        const validation = await configManager.validateFullConfig();
        return { success: true, validation };
    } catch (error) {
        console.error('Failed to validate config:', error);
        return { success: false, error: error.message };
    }
});

// MCP 서버 자동 시작 핸들러
ipcMain.handle('mcp:start-servers', async () => {
    try {
        console.log('Starting MCP servers via IPC...');
        await startMcpServers();

        // 시작 후 상태 확인
        const status = mcpClientManager.getServerStatus();
        console.log('MCP servers started. Status:', status);

        return { success: true, status };
    } catch (error) {
        console.error('Failed to start MCP servers via IPC:', error);
        return { success: false, error: error.message };
    }
});

// MCP 진단 핸들러
ipcMain.handle('mcp:diagnose', async () => {
    try {
        console.log('🔍 Running MCP diagnosis...');

        const diagnosis = {
            serverProcesses: {},
            clientConnections: {},
            availableTools: {},
            summary: {
                runningServers: 0,
                connectedClients: 0,
                totalTools: 0,
                errors: []
            }
        };

        // 1. 서버 프로세스 상태 확인
        for (const [name, process] of mcpServerProcesses.entries()) {
            diagnosis.serverProcesses[name] = {
                running: !process.killed && process.exitCode === null,
                pid: process.pid,
                exitCode: process.exitCode,
                killed: process.killed
            };

            if (diagnosis.serverProcesses[name].running) {
                diagnosis.summary.runningServers++;
            }
        }

        // 2. 클라이언트 연결 상태 확인
        const clientStatus = mcpClientManager.getServerStatus();
        diagnosis.clientConnections = clientStatus;

        for (const [name, status] of Object.entries(clientStatus)) {
            if (status.connected) {
                diagnosis.summary.connectedClients++;
                diagnosis.summary.totalTools += status.toolsCount || 0;
            }

            diagnosis.availableTools[name] = {
                tools: status.toolsCount || 0,
                resources: status.resourcesCount || 0,
                prompts: status.promptsCount || 0
            };
        }

        // 3. 문제 진단
        if (diagnosis.summary.runningServers === 0) {
            diagnosis.summary.errors.push('No MCP server processes are running');
        }

        if (diagnosis.summary.connectedClients === 0) {
            diagnosis.summary.errors.push('No MCP clients are connected');
        }

        if (diagnosis.summary.totalTools === 0) {
            diagnosis.summary.errors.push('No MCP tools are available');
        }

        console.log('🏁 MCP diagnosis completed:', diagnosis.summary);
        return { success: true, diagnosis };

    } catch (error) {
        console.error('❌ MCP diagnosis failed:', error);
        return { success: false, error: error.message };
    }
});

// MCP 서버 연결 테스트 핸들러
ipcMain.handle('mcp:test-connection', async () => {
    try {
        console.log('Testing MCP server connections...');

        const results = {};

        // 내장 서버 테스트
        for (const [name, server] of mcpServers.entries()) {
            try {
                if (server && typeof server.handleToolCall === 'function') {
                    // 간단한 도구 호출로 연결 테스트
                    const testResponse = await server.handleToolCall('test-id', {
                        name: 'list_files',
                        arguments: { path: process.cwd() }
                    });

                    results[name] = {
                        status: 'connected',
                        type: 'internal',
                        port: server.port,
                        response: testResponse ? 'success' : 'no_response'
                    };
                } else {
                    results[name] = {
                        status: 'disconnected',
                        type: 'internal',
                        error: 'Server not responding'
                    };
                }
            } catch (error) {
                results[name] = {
                    status: 'error',
                    type: 'internal',
                    error: error.message
                };
            }
        }

        // 외부 서버 테스트
        for (const [name, process] of mcpServerProcesses.entries()) {
            try {
                if (process && !process.killed && process.exitCode === null) {
                    results[name] = {
                        status: 'running',
                        type: 'external',
                        pid: process.pid
                    };
                } else {
                    results[name] = {
                        status: 'stopped',
                        type: 'external',
                        error: 'Process not running'
                    };
                }
            } catch (error) {
                results[name] = {
                    status: 'error',
                    type: 'external',
                    error: error.message
                };
            }
        }

        console.log('MCP connection test results:', results);
        return { success: true, results };
    } catch (error) {
        console.error('Failed to test MCP connections:', error);
        return { success: false, error: error.message };
    }
});

// API 키 직접 가져오기 핸들러 (디버깅용)
ipcMain.handle('get-api-key-direct', async () => {
    try {
        console.log('IPC: get-api-key-direct called');
        
        if (!configManager.config) {
            await configManager.loadConfig();
        }
        
        const apiKey = configManager.config?.llmConfig?.directApiKey || '';
        console.log('IPC: API key retrieved directly successfully:', !!apiKey);
        return { success: true, apiKey };
    } catch (error) {
        console.error('IPC: Failed to get API key directly:', error);
        return { success: false, error: error.message };
    }
});

// LLM 설정 가져오기 핸들러
ipcMain.handle('get-llm-config', async () => {
    try {
        console.log('=== IPC: get-llm-config called ===');
        const config = await configManager.loadConfig();

        console.log('Raw config loaded:', {
            hasLlmConfig: !!config.llmConfig,
            llmConfigKeys: config.llmConfig ? Object.keys(config.llmConfig) : [],
            apiType: config.llmConfig?.apiType,
            apiUrl: config.llmConfig?.apiUrl,
            model: config.llmConfig?.model
        });

        // LLM 관련 설정만 추출
        const llmConfig = {
            apiType: config.llmConfig?.apiType || 'local',
            apiUrl: config.llmConfig?.apiUrl || 'http://localhost:11434',
            model: config.llmConfig?.model || 'llama4-maverick',
            temperature: config.llmConfig?.temperature || 0.7,
            maxTokens: config.llmConfig?.maxTokens || 2048,
            customApiUrl: config.llmConfig?.customApiUrl || '',
            directApiKey: config.llmConfig?.directApiKey || '' // 직접 저장된 API 키 포함
        };

        console.log('Extracted LLM config before API key processing:', {
            apiType: llmConfig.apiType,
            apiUrl: llmConfig.apiUrl,
            model: llmConfig.model,
            temperature: llmConfig.temperature,
            maxTokens: llmConfig.maxTokens,
            hasDirectApiKey: !!llmConfig.directApiKey
        });

        // API 키가 필요한 경우 추가
        if (llmConfig.apiType === 'openai' || llmConfig.apiType === 'anthropic' || llmConfig.apiType === 'custom') {
            try {
                const apiKey = configManager.getApiKey();
                if (apiKey) {
                    llmConfig.apiKey = apiKey;
                    console.log('Encrypted API key retrieved and added to config');
                } else {
                    console.log('No encrypted API key found');
                }
            } catch (keyError) {
                console.error('Error getting API key:', keyError);
            }
        } else {
            console.log(`API type is ${llmConfig.apiType}, no API key needed`);
        }

        console.log('=== IPC: get-llm-config final result ===', {
            apiType: llmConfig.apiType,
            apiUrl: llmConfig.apiUrl,
            model: llmConfig.model,
            temperature: llmConfig.temperature,
            maxTokens: llmConfig.maxTokens,
            hasApiKey: !!llmConfig.apiKey,
            hasDirectApiKey: !!llmConfig.directApiKey
        });

        return { success: true, config: llmConfig };
    } catch (error) {
        console.error('IPC: Failed to get LLM config:', error);
        const fallbackConfig = {
            apiType: 'local',
            apiUrl: 'http://localhost:11434',
            model: 'llama4-maverick',
            temperature: 0.7,
            maxTokens: 2048
        };
        console.log('Returning fallback config:', fallbackConfig);
        return {
            success: false,
            error: error.message,
            config: fallbackConfig
        };
    }
});

// LLM 설정 저장 핸들러
ipcMain.handle('save-llm-config', async (event, llmConfig) => {
    try {
        console.log('IPC: save-llm-config called with:', llmConfig);

        // 전체 설정 로드
        const config = await configManager.loadConfig();

        // LLM 설정 업데이트
        config.llmConfig = {
            ...config.llmConfig,
            ...llmConfig
        };

        // 설정 저장
        await configManager.saveConfig(config);

        console.log('IPC: LLM config saved successfully');

        // 모든 렌더러 프로세스에 LLM 설정 변경 알림
        BrowserWindow.getAllWindows().forEach(window => {
            window.webContents.send('llm-config-changed', llmConfig);
        });

        return { success: true };
    } catch (error) {
        console.error('IPC: Failed to save LLM config:', error);
        return { success: false, error: error.message };
    }
});

// LLM 설정 변경 알림 핸들러
ipcMain.handle('notify-llm-config-changed', async (event, llmConfig) => {
    try {
        console.log('IPC: notify-llm-config-changed called');

        // 모든 렌더러 프로세스에 알림 (설정 창 제외)
        BrowserWindow.getAllWindows().forEach(window => {
            if (window.webContents !== event.sender) {
                window.webContents.send('llm-config-changed', llmConfig);
            }
        });

        return { success: true };
    } catch (error) {
        console.error('IPC: Failed to notify LLM config change:', error);
        return { success: false, error: error.message };
    }
});

// API 요청 핸들러 - 메인 프로세스에서 API 호출 처리
ipcMain.handle('api-request', async (event, { apiType, url, method, headers, body }) => {
    try {
        console.log(`IPC: api-request called for ${apiType} API`);
        console.log('URL:', url);
        console.log('Method:', method);
        
        // API 키 가져오기 (필요한 경우)
        if ((apiType === 'openai' || apiType === 'anthropic' || apiType === 'custom') && 
            headers && headers.Authorization === 'Bearer FETCH_FROM_SECURE_STORAGE') {
            try {
                const apiKey = configManager.getApiKey();
                if (apiKey) {
                    headers.Authorization = `Bearer ${apiKey}`;
                } else {
                    throw new Error(`${apiType} API 키가 설정되지 않았습니다.`);
                }
            } catch (keyError) {
                console.error('Error getting API key:', keyError);
                throw new Error('API 키를 가져오는 중 오류가 발생했습니다.');
            }
        }
        
        // API 요청 보내기
        const response = await fetch(url, {
            method: method || 'GET',
            headers: headers || {},
            body: body ? JSON.stringify(body) : undefined
        });
        
        // 응답 처리
        const responseData = await response.json();
        
        return {
            success: true,
            status: response.status,
            data: responseData
        };
    } catch (error) {
        console.error('IPC: API request failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

// MCP 서버를 통한 파일 접근 핸들러 개선
ipcMain.handle('mcp:file-access', async (_, params) => {
    try {
        const { operation, path, content } = params;
        console.log(`MCP file access: ${operation} for path ${path}`);
        
        // 파일 시스템 모듈 로드
        const fs = require('fs').promises;
        const pathModule = require('path');
        
        // 파일 작업 수행
        let result;
        switch (operation) {
            case 'read':
                try {
                    const data = await fs.readFile(path, 'utf8');
                    console.log(`Successfully read file: ${path}, size: ${data.length} bytes`);
                    result = { success: true, data };
                } catch (readError) {
                    console.error(`Error reading file: ${path}`, readError);
                    result = { success: false, error: readError.message };
                }
                break;
                
            case 'write':
                try {
                    // 디렉토리 생성
                    const dir = pathModule.dirname(path);
                    await fs.mkdir(dir, { recursive: true }).catch(err => {
                        console.log(`Directory creation: ${err ? err.message : 'success'}`);
                    });
                    
                    await fs.writeFile(path, content, 'utf8');
                    console.log(`Successfully wrote to file: ${path}`);
                    result = { success: true };
                } catch (writeError) {
                    console.error(`Error writing to file: ${path}`, writeError);
                    result = { success: false, error: writeError.message };
                }
                break;
                
            case 'list':
                try {
                    const files = await fs.readdir(path);
                    console.log(`Listed directory: ${path}, found ${files.length} files`);
                    
                    const fileStats = await Promise.all(
                        files.map(async (file) => {
                            const filePath = pathModule.join(path, file);
                            try {
                                const stats = await fs.stat(filePath);
                                return {
                                    name: file,
                                    path: filePath,
                                    isDirectory: stats.isDirectory(),
                                    size: stats.size,
                                    modified: stats.mtime
                                };
                            } catch (statError) {
                                console.warn(`Could not stat file ${filePath}: ${statError.message}`);
                                return { name: file, path: filePath, error: statError.message };
                            }
                        })
                    );
                    result = { success: true, files: fileStats };
                } catch (listError) {
                    console.error(`Error listing directory: ${path}`, listError);
                    result = { success: false, error: listError.message };
                }
                break;
                
            default:
                result = { success: false, error: `Unsupported operation: ${operation}` };
        }
        
        return result;
    } catch (error) {
        console.error('MCP file access error:', error);
        return { success: false, error: error.message };
    }
});

// LLM과 MCP 서버 연동 핸들러 개선
ipcMain.handle('llm:use-mcp-tools', async (_, params) => {
    try {
        const { tool, args } = params;
        console.log(`LLM using MCP tool: ${tool}`, args);

        // 현재 LLM 설정 가져오기
        const config = await configManager.loadConfig();
        const llmConfig = config.llmConfig || {};

        console.log(`Current LLM API type: ${llmConfig.apiType}`);

        // MCP 서버를 통한 도구 실행
        const mcpResult = await executeMcpTool(tool, args, llmConfig);
        if (mcpResult.success) {
            return mcpResult;
        }

        // 폴백: 직접 파일 시스템 접근
        console.log(`MCP server failed, using direct file system access for tool: ${tool}`);
        const fs = require('fs').promises;
        const pathModule = require('path');

        // 도구 실행 - 직접 파일 시스템 접근
        let result;
        
        switch (tool) {
            case 'read_file':
                try {
                    console.log(`Reading file: ${args.path}`);
                    const data = await fs.readFile(args.path, 'utf8');
                    console.log(`Successfully read file: ${args.path}, size: ${data.length} bytes`);
                    result = { success: true, data };
                } catch (error) {
                    console.error(`Error reading file: ${args.path}`, error);
                    result = { success: false, error: error.message };
                }
                break;
                
            case 'write_file':
                try {
                    console.log(`Writing to file: ${args.path}`);
                    // 디렉토리 생성
                    const dir = pathModule.dirname(args.path);
                    await fs.mkdir(dir, { recursive: true }).catch(err => {
                        console.log(`Directory creation: ${err ? err.message : 'success'}`);
                    });
                    
                    await fs.writeFile(args.path, args.content, 'utf8');
                    console.log(`Successfully wrote to file: ${args.path}`);
                    result = { success: true };
                } catch (error) {
                    console.error(`Error writing file: ${args.path}`, error);
                    result = { success: false, error: error.message };
                }
                break;
                
            case 'list_files':
                try {
                    console.log(`Listing directory: ${args.path}`);
                    const files = await fs.readdir(args.path);
                    console.log(`Listed directory: ${args.path}, found ${files.length} files`);
                    
                    const fileStats = await Promise.all(
                        files.map(async (file) => {
                            const filePath = pathModule.join(args.path, file);
                            try {
                                const stats = await fs.stat(filePath);
                                return {
                                    name: file,
                                    path: filePath,
                                    isDirectory: stats.isDirectory(),
                                    size: stats.size,
                                    modified: stats.mtime
                                };
                            } catch (statError) {
                                console.warn(`Could not stat file ${filePath}: ${statError.message}`);
                                return { name: file, path: filePath, error: statError.message };
                            }
                        })
                    );
                    result = { success: true, files: fileStats };
                } catch (error) {
                    console.error(`Error listing directory: ${args.path}`, error);
                    result = { success: false, error: error.message };
                }
                break;
                
            default:
                result = { success: false, error: `Unsupported tool: ${tool}` };
        }
        
        console.log(`MCP tool execution result:`, result.success ? 'Success' : 'Failed');
        return result;
    } catch (error) {
        console.error('LLM MCP tool error:', error);
        return { success: false, error: error.message };
    }
});

// 앱 이벤트 핸들러
app.whenReady().then(async () => {
    const os = require('os');
    console.log('System information:');
    console.log(`OS: ${os.type()} ${os.release()} ${os.arch()}`);
    console.log(`CPU: ${os.cpus()[0].model} (${os.cpus().length} cores)`);
    console.log(`Memory: ${Math.round(os.totalmem() / (1024 * 1024 * 1024))} GB`);
    console.log(`Free memory: ${Math.round(os.freemem() / (1024 * 1024 * 1024))} GB`);
    
    // Electron 및 Chrome 버전 정보
    console.log(`Electron: ${process.versions.electron}`);
    console.log(`Chrome: ${process.versions.chrome}`);
    console.log(`Node.js: ${process.versions.node}`);
    
    // GPU 정보 (안전 모드가 아닌 경우에만)
    if (!isSafeGraphicsMode) {
        try {
            app.getGPUInfo('basic').then(info => {
                console.log('GPU Info:', info);
            }).catch(err => {
                console.error('Failed to get GPU info:', err);
            });
        } catch (error) {
            console.error('Error getting GPU info:', error);
        }
    }
    
    try {
        createWindow();
        createMenu();

        // MCP 서버 시작 (내장 및 외부)
        await startMcpServers();

        // macOS에서 독 아이콘 클릭 시 윈도우 재생성
        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                createWindow();
            }
        });
    } catch (error) {
        console.error('Error during app initialization:', error);
        dialog.showErrorBox(
            'Application Error',
            `An error occurred during initialization: ${error.message}`
        );
    }
}).catch(error => {
    console.error('Failed to initialize application:', error);
    dialog.showErrorBox(
        'Critical Error',
        `Failed to start application: ${error.message}`
    );
    app.quit();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('before-quit', () => {
    stopAllMcpServers();
});

// 보안 설정
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
    });

    contents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        if (parsedUrl.origin !== 'file://') {
            event.preventDefault();
        }
    });
});

// 단일 인스턴스 강제
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}

// 설정 창 열기 함수
function openSettingsWindow() {
    if (settingsWindow) {
        settingsWindow.focus();
        return;
    }

    settingsWindow = new BrowserWindow({
        width: 900,
        height: 700,
        title: 'MCP Desktop Clone - 설정',
        parent: mainWindow,
        modal: true,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        }
    });

    settingsWindow.loadFile('settings.html');

    // 개발 도구 열기 (개발 중에만 사용)
    if (isDev) {
        settingsWindow.webContents.openDevTools();
    }

    settingsWindow.on('closed', () => {
        settingsWindow = null;
    });
}

// 설정 열기 IPC 핸들러 추가
ipcMain.handle('open-settings', () => {
    openSettingsWindow();
});

// 모델명 업데이트 핸들러 개선
ipcMain.handle('update-model-name', async (event, modelName) => {
    try {
        console.log(`Updating model name to: ${modelName}`);
        
        // 모든 창에 모델명 변경 이벤트 전송
        BrowserWindow.getAllWindows().forEach(win => {
            try {
                if (win && win.webContents && !win.isDestroyed()) {
                    console.log(`Sending model-changed event to window ${win.id}`);
                    win.webContents.send('model-changed', modelName);
                }
            } catch (error) {
                console.error(`Failed to send model-changed event to window ${win.id}:`, error);
            }
        });

        return { success: true };
    } catch (error) {
        console.error('Failed to update model name:', error);
        return { success: false, error: error.message };
    }
});

// 설정 패널만 숨기는 핸들러
ipcMain.handle('hide-settings-panel', () => {
    console.log('IPC: hide-settings-panel called');
    
    // 메인 창에 설정 패널 숨김 이벤트 전송
    if (mainWindow) {
        mainWindow.webContents.send('hide-settings-panel');
        return { success: true };
    }
    
    return { success: false, error: '메인 창을 찾을 수 없습니다.' };
});

// 설정 저장 완료 이벤트 핸들러
ipcMain.handle('settings-saved', () => {
    console.log('IPC: settings-saved called');
    
    // 메인 창에 설정 저장 완료 이벤트 전송
    if (mainWindow) {
        mainWindow.webContents.send('settings-saved');
        return { success: true };
    }
    
    return { success: false, error: '메인 창을 찾을 수 없습니다.' };
});

// 일반 모드로 재시작
ipcMain.handle('restart-in-normal-mode', () => {
    console.log('Restarting in normal mode...');
    const args = process.argv.slice(1).filter(arg => arg !== '--safe-graphics-mode');
    app.relaunch({ args });
    app.exit(0);
    return { success: true };
});

// 안전 모드로 재시작
ipcMain.handle('restart-in-safe-mode', () => {
    console.log('Restarting in safe graphics mode...');
    const args = process.argv.slice(1);
    if (!args.includes('--safe-graphics-mode')) {
        args.push('--safe-graphics-mode');
    }
    app.relaunch({ args });
    app.exit(0);
    return { success: true };
});

// 추가적인 앱 이벤트 핸들러
app.on('window-all-closed', () => {
    console.log('All windows closed');
    // macOS가 아닌 경우 앱 종료
    if (process.platform !== 'darwin') {
        console.log('Quitting app (not macOS)');
        app.quit();
    }
});

app.on('activate', () => {
    console.log('App activated');
    // macOS에서 독 아이콘 클릭 시 창이 없으면 새 창 생성
    if (BrowserWindow.getAllWindows().length === 0) {
        console.log('No windows found, creating main window');
        createWindow();
    }
});

// 앱이 완전히 종료될 때
app.on('will-quit', () => {
    console.log('App will quit');
    // 추가 정리 작업이 필요하면 여기서 수행
});
