// Settings page functionality
class SettingsManager {
    constructor() {
        this.config = null;
        this.mcpServers = new Map();
        this.serverStatus = {};
        this.activeSection = 'general';
        this.tempConfig = null; // 임시 설정 객체 추가
        this.allowedDirectories = []; // 허용 디렉토리 배열 추가

        this.init();

        // 언어 변경 이벤트 리스너 설정
        if (window.electronAPI && window.electronAPI.onLanguageChanged) {
            window.electronAPI.onLanguageChanged((language) => {
                console.log('Language changed event received:', language);
                this.applyLanguage(language);
                
                // 언어 선택 드롭다운 업데이트
                const languageSelect = document.getElementById('language');
                if (languageSelect && languageSelect.value !== language) {
                    languageSelect.value = language;
                }
            });
        }
    }

    async init() {
        try {
            console.log('Starting settings initialization...');

            // Font Awesome 아이콘 로드
            try {
                const fontAwesomeLink = document.createElement('link');
                fontAwesomeLink.rel = 'stylesheet';
                fontAwesomeLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
                document.head.appendChild(fontAwesomeLink);
                console.log('Font Awesome loaded successfully');
            } catch (fontError) {
                console.warn('Failed to load Font Awesome, continuing anyway:', fontError);
            }

            // 설정 로드
            try {
                console.log('Attempting to load config...');
                let loadedConfig = null;

                // 1. electronAPI를 통한 로드 시도 (iframe 환경 고려)
                const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
                if (parentElectronAPI && parentElectronAPI.getConfig) {
                    console.log('Loading via electronAPI...');
                    loadedConfig = await parentElectronAPI.getConfig();
                    console.log('ElectronAPI load result:', loadedConfig);
                } else {
                    console.log('ElectronAPI not available, using localStorage fallback...');

                    // 2. localStorage에서 로드
                    try {
                        const storedConfig = localStorage.getItem('mcpDesktopConfig');
                        if (storedConfig) {
                            loadedConfig = JSON.parse(storedConfig);
                            console.log('Config loaded from localStorage:', loadedConfig);
                        } else {
                            console.log('No config found in localStorage');
                        }
                    } catch (storageError) {
                        console.error('Failed to load from localStorage:', storageError);
                    }
                }

                if (loadedConfig) {
                    console.log('Config loaded successfully');
                    this.config = { ...this.getDefaultConfig(), ...loadedConfig };
                } else {
                    console.log('No config loaded, using defaults');
                    this.config = this.getDefaultConfig();

                    // 기본 설정 저장 시도
                    try {
                        if (parentElectronAPI && parentElectronAPI.saveConfig) {
                            await parentElectronAPI.saveConfig(this.config);
                            console.log('Default config saved via electronAPI');
                        } else {
                            localStorage.setItem('mcpDesktopConfig', JSON.stringify(this.config));
                            console.log('Default config saved to localStorage');
                        }
                    } catch (saveError) {
                        console.warn('Failed to save default config:', saveError);
                    }
                }
            } catch (configError) {
                console.error('Error loading config, using defaults:', configError);
                this.config = this.getDefaultConfig();
            }

            console.log('Current config:', this.config);

            // UI 초기화
            this.setupEventListeners();
            this.populateForm();

            // 첫 번째 탭 활성화
            this.showSection('general');

            // MCP 서버 관련 초기화 - 설정 로드 직후 수행
            console.log('Initializing MCP servers...');
            await this.loadMCPServers();
            this.setupServerFormListeners();

            // 서버 상태 확인은 약간 지연
            setTimeout(() => {
                this.checkMcpServerStatus();
            }, 500);

            // 저장 및 취소 버튼 이벤트 리스너 명시적 설정
            const saveBtn = document.getElementById('saveBtn');
            if (saveBtn) {
                console.log('Adding click listener to saveBtn');
                saveBtn.addEventListener('click', () => {
                    console.log('=== Save button clicked ===');
                    console.log('Current form values before save:');
                    console.log('- LLM Model Select:', document.getElementById('llmModelSelect')?.value);
                    console.log('- API Type:', document.getElementById('apiType')?.value);
                    console.log('- API URL:', document.getElementById('apiUrl')?.value);
                    console.log('- Model Name:', document.getElementById('modelName')?.value);
                    console.log('- Temperature:', document.getElementById('temperature')?.value);
                    console.log('- Max Tokens:', document.getElementById('maxTokens')?.value);
                    console.log('- Theme:', document.getElementById('theme')?.value);
                    console.log('- Language:', document.getElementById('language')?.value);

                    this.saveSettings();
                });
            } else {
                console.warn('saveBtn not found in DOM');
            }

            const cancelBtn = document.getElementById('cancelBtn');
            if (cancelBtn) {
                console.log('Adding click listener to cancelBtn');
                cancelBtn.addEventListener('click', () => {
                    console.log('Cancel button clicked');
                    this.closeSettings();
                });
            } else {
                console.warn('cancelBtn not found in DOM');
            }

            console.log('Settings initialized successfully');
        } catch (error) {
            console.error('Failed to initialize settings:', error);
            alert('설정을 초기화하는 데 실패했습니다: ' + error.message);
        }
    }

    // MCP 클라이언트 상태 새로고침
    async refreshClientStatus() {
        try {
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (parentElectronAPI && parentElectronAPI.mcpClientGetStatus) {
                const result = await parentElectronAPI.mcpClientGetStatus();
                if (result.success) {
                    this.updateClientStatusUI(result.status);
                } else {
                    this.showNotification('클라이언트 상태 조회 실패: ' + result.error, 'error');
                }
            } else {
                this.showNotification('MCP 클라이언트 기능을 사용할 수 없습니다.', 'warning');
            }
        } catch (error) {
            console.error('Failed to refresh client status:', error);
            this.showNotification('클라이언트 상태 새로고침 실패: ' + error.message, 'error');
        }
    }

    // MCP 도구 목록 새로고침
    async refreshMcpTools() {
        try {
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (parentElectronAPI && parentElectronAPI.mcpClientGetTools) {
                const result = await parentElectronAPI.mcpClientGetTools();
                if (result.success) {
                    this.updateToolsListUI(result.tools);
                } else {
                    this.showNotification('도구 목록 조회 실패: ' + result.error, 'error');
                }
            } else {
                this.showNotification('MCP 클라이언트 기능을 사용할 수 없습니다.', 'warning');
            }
        } catch (error) {
            console.error('Failed to refresh MCP tools:', error);
            this.showNotification('도구 목록 새로고침 실패: ' + error.message, 'error');
        }
    }

    // 클라이언트 상태 UI 업데이트
    updateClientStatusUI(clientStatus) {
        const container = document.getElementById('mcpClientStatus');
        if (!container) return;

        container.innerHTML = '';

        if (!clientStatus || Object.keys(clientStatus).length === 0) {
            container.innerHTML = '<div class="empty-state">연결된 MCP 클라이언트가 없습니다.</div>';
            return;
        }

        for (const [serverName, status] of Object.entries(clientStatus)) {
            const statusElement = document.createElement('div');
            statusElement.className = 'client-status-item';
            statusElement.innerHTML = `
                <div class="status-header">
                    <span class="server-name">${serverName}</span>
                    <span class="status-indicator ${status.connected ? 'connected' : 'disconnected'}">
                        ${status.connected ? '연결됨' : '연결 안됨'}
                    </span>
                </div>
                <div class="status-details">
                    <small>도구: ${status.toolsCount || 0}개, 리소스: ${status.resourcesCount || 0}개</small>
                </div>
            `;
            container.appendChild(statusElement);
        }
    }

    // 도구 목록 UI 업데이트
    updateToolsListUI(tools) {
        const container = document.getElementById('mcpToolsList');
        if (!container) return;

        container.innerHTML = '';

        if (!tools || tools.length === 0) {
            container.innerHTML = '<div class="empty-state">사용 가능한 도구가 없습니다.</div>';
            return;
        }

        tools.forEach(tool => {
            const toolElement = document.createElement('div');
            toolElement.className = 'tool-item';
            toolElement.innerHTML = `
                <div class="tool-header">
                    <span class="tool-name">${tool.name}</span>
                    <span class="tool-server">${tool.serverName}</span>
                </div>
                <div class="tool-description">
                    <small>${tool.description || '설명 없음'}</small>
                </div>
                <button class="btn btn-sm btn-secondary test-tool-btn" data-tool-name="${tool.name}">
                    테스트
                </button>
            `;

            // 도구 테스트 버튼 이벤트 리스너
            const testBtn = toolElement.querySelector('.test-tool-btn');
            testBtn.addEventListener('click', () => this.testMcpTool(tool.name));

            container.appendChild(toolElement);
        });
    }

    // MCP 도구 테스트
    async testMcpTool(toolName) {
        try {
            console.log(`🧪 Testing MCP tool: ${toolName}`);
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;

            if (parentElectronAPI && parentElectronAPI.mcpClientCallTool) {
                // 도구별 테스트 인수 설정
                const testArgs = this.getTestArgsForTool(toolName);
                console.log(`📋 Test arguments for ${toolName}:`, testArgs);

                const result = await parentElectronAPI.mcpClientCallTool(toolName, testArgs);
                console.log(`📊 Test result for ${toolName}:`, result);

                if (result.success) {
                    this.showNotification(`도구 '${toolName}' 테스트 성공`, 'success');
                    console.log('✅ Tool test result:', result.result);
                } else {
                    this.showNotification(`도구 '${toolName}' 테스트 실패: ${result.error}`, 'error');
                    console.error('❌ Tool test failed:', result.error);
                }
            } else {
                console.warn('⚠️ MCP client API not available');
                this.showNotification('MCP 클라이언트 기능을 사용할 수 없습니다.', 'warning');
            }
        } catch (error) {
            console.error('❌ Failed to test MCP tool:', error);
            this.showNotification(`도구 테스트 실패: ${error.message}`, 'error');
        }
    }

    // 도구별 테스트 인수 생성
    getTestArgsForTool(toolName) {
        switch (toolName) {
            case 'list_files':
                return { path: '.' };
            case 'read_file':
                return { path: './package.json' };
            case 'write_file':
                return {
                    path: './test-mcp-ui.txt',
                    content: `MCP UI 테스트 파일\n생성 시간: ${new Date().toISOString()}`
                };
            case 'search_files':
                return {
                    path: '.',
                    pattern: '*.json',
                    recursive: false
                };
            case 'execute_command':
                return {
                    command: 'echo',
                    args: ['MCP 테스트 성공']
                };
            default:
                return {};
        }
    }

    // MCP 설정을 별도 파일에 저장하는 메서드
    async saveMcpConfigSafely(mcpServers) {
        try {
            console.log('Saving MCP config to mcp_desktop_config.json...');

            // Claude Desktop 형식으로 변환 (command와 args만 포함)
            const mcpConfig = {
                mcpServers: {}
            };

            // Map을 순회하면서 Claude Desktop 형식으로 변환
            mcpServers.forEach((serverConfig, serverName) => {
                mcpConfig.mcpServers[serverName] = {
                    command: serverConfig.command,
                    args: serverConfig.args || []
                };

                // env가 있고 비어있지 않은 경우에만 추가
                if (serverConfig.env && Object.keys(serverConfig.env).length > 0) {
                    mcpConfig.mcpServers[serverName].env = serverConfig.env;
                }
            });

            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (parentElectronAPI && parentElectronAPI.saveMcpConfig) {
                await parentElectronAPI.saveMcpConfig(mcpConfig);
                console.log('✅ MCP config saved to mcp_desktop_config.json via electronAPI');
            } else if (parentElectronAPI && parentElectronAPI.writeFile) {
                // 직접 파일 쓰기 시도
                const jsonData = JSON.stringify(mcpConfig, null, 2);
                await parentElectronAPI.writeFile('./mcp_desktop_config.json', jsonData);
                console.log('✅ MCP config saved to mcp_desktop_config.json via writeFile');
            } else {
                // 웹 환경에서는 로컬 스토리지에 저장
                localStorage.setItem('mcpDesktopConfig', JSON.stringify(mcpConfig));
                console.log('✅ MCP config saved to localStorage (web environment)');
            }

            return true;
        } catch (error) {
            console.error('❌ Failed to save MCP config safely:', error);
            throw error;
        }
    }

    // Claude Desktop 설정 내보내기
    async exportClaudeConfig() {
        try {
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (parentElectronAPI && parentElectronAPI.showSaveDialog) {
                const result = await parentElectronAPI.showSaveDialog({
                    title: 'Claude Desktop 설정 내보내기',
                    defaultPath: 'mcp_desktop_config.json',
                    filters: [
                        { name: 'JSON Files', extensions: ['json'] },
                        { name: 'All Files', extensions: ['*'] }
                    ]
                });

                if (!result.canceled && result.filePath) {
                    // MCP 서버 설정을 Claude Desktop 형식으로 변환
                    const claudeConfig = {
                        mcpServers: Object.fromEntries(this.mcpServers)
                    };

                    await parentElectronAPI.writeFile(result.filePath, JSON.stringify(claudeConfig, null, 2));
                    this.showNotification('Claude Desktop 설정이 내보내졌습니다.', 'success');
                }
            } else {
                this.showNotification('파일 저장 기능을 사용할 수 없습니다.', 'warning');
            }
        } catch (error) {
            console.error('Failed to export Claude config:', error);
            this.showNotification('설정 내보내기 실패: ' + error.message, 'error');
        }
    }

    // Claude Desktop 설정 가져오기
    async importClaudeConfig() {
        try {
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (parentElectronAPI && parentElectronAPI.showOpenDialog) {
                const result = await parentElectronAPI.showOpenDialog({
                    title: 'Claude Desktop 설정 가져오기',
                    filters: [
                        { name: 'JSON Files', extensions: ['json'] },
                        { name: 'All Files', extensions: ['*'] }
                    ],
                    properties: ['openFile']
                });

                if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
                    const fileContent = await parentElectronAPI.readFile(result.filePaths[0]);
                    if (fileContent.success) {
                        const claudeConfig = JSON.parse(fileContent.data);

                        if (claudeConfig.mcpServers) {
                            // 기존 서버들과 병합
                            for (const [name, config] of Object.entries(claudeConfig.mcpServers)) {
                                this.mcpServers.set(name, config);
                            }

                            // MCP 설정을 별도 파일에 저장
                            await this.saveMcpConfigSafely(this.mcpServers);

                            // UI 업데이트
                            this.renderMCPServers();
                            this.showNotification('Claude Desktop 설정이 가져와졌습니다.', 'success');
                        } else {
                            this.showNotification('유효한 Claude Desktop 설정 파일이 아닙니다.', 'error');
                        }
                    } else {
                        this.showNotification('파일 읽기 실패: ' + fileContent.error, 'error');
                    }
                }
            } else {
                this.showNotification('파일 열기 기능을 사용할 수 없습니다.', 'warning');
            }
        } catch (error) {
            console.error('Failed to import Claude config:', error);
            this.showNotification('설정 가져오기 실패: ' + error.message, 'error');
        }
    }



    getDefaultConfig() {
        return {
            theme: 'dark',
            language: 'ko',
            llmConfig: {
                apiType: 'anthropic',
                apiUrl: 'https://api.anthropic.com/v1/messages',
                model: 'claude-3-5-sonnet-20241022',
                temperature: 0.7,
                maxTokens: 8192,
                promptSettings: {
                    systemPrompt: '당신은 도움이 되고 정확한 AI 어시스턴트입니다. 사용자의 질문에 친절하고 상세하게 답변해주세요.',
                    userPromptTemplate: '{user_input}',
                    conversationStarters: [
                        '안녕하세요! 무엇을 도와드릴까요?',
                        '오늘 어떤 것에 대해 이야기하고 싶으신가요?',
                        '궁금한 것이 있으시면 언제든 물어보세요!'
                    ],
                    enableSystemPrompt: true,
                    enablePromptTemplate: false
                }
            },
            appearance: {
                fontSize: '14',
                fontFamily: 'system'
            },
            general: {
                startAtLogin: false,
                minimizeToTray: true
            },
            advanced: {
                enableDevTools: false,
                enableLogging: false
            },
            mcpServers: {}
        };
    }

    setupEventListeners() {
        console.log('Setting up event listeners...');

        // Navigation - 탭 클릭 이벤트 개선
        const navButtons = document.querySelectorAll('.settings-nav-btn');
        console.log(`Found ${navButtons.length} navigation buttons`);

        navButtons.forEach((btn, index) => {
            const section = btn.dataset.section;
            console.log(`Setting up nav button ${index}: ${section}`);

            // 버튼 속성 확인
            console.log(`Button ${index} properties:`, {
                tagName: btn.tagName,
                className: btn.className,
                dataset: btn.dataset,
                disabled: btn.disabled,
                style: btn.style.cssText
            });

            // 기존 이벤트 리스너 제거 (중복 방지)
            btn.removeEventListener('click', this.handleNavClick);

            // 여러 이벤트 타입으로 리스너 추가
            const clickHandler = (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(`Navigation button clicked: ${section}`, e);
                this.showSection(section);
            };

            btn.addEventListener('click', clickHandler);
            btn.addEventListener('mousedown', clickHandler);

            // 키보드 접근성
            btn.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    console.log(`Navigation button activated via keyboard: ${section}`);
                    this.showSection(section);
                }
            });

            // 포커스 가능하도록 설정
            btn.setAttribute('tabindex', '0');
        });

        // Form elements
        this.setupFormListeners();

        // Buttons - 안전한 이벤트 리스너 설정
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSettings());
        }

        const cancelBtn = document.getElementById('cancelBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.closeSettings());
        }

        // LLM settings
        const testConnectionBtn = document.getElementById('testConnection');
        if (testConnectionBtn) {
            testConnectionBtn.addEventListener('click', () => this.testLLMConnection());
        }

        const temperatureSlider = document.getElementById('temperature');
        if (temperatureSlider) {
            temperatureSlider.addEventListener('input', (e) => {
                const temperatureValue = document.getElementById('temperatureValue');
                if (temperatureValue) {
                    temperatureValue.textContent = e.target.value;
                }
            });
        }

        // MCP Server management
        const addServerBtn = document.getElementById('addServerBtn');
        if (addServerBtn) {
            addServerBtn.addEventListener('click', () => this.showAddServerForm());
        }

        const saveServerBtn = document.getElementById('saveServerBtn');
        if (saveServerBtn) {
            saveServerBtn.addEventListener('click', () => this.saveServer());
        }

        const cancelServerBtn = document.getElementById('cancelServerBtn');
        if (cancelServerBtn) {
            cancelServerBtn.addEventListener('click', () => this.hideAddServerForm());
        }

        // MCP 클라이언트 관련 이벤트 리스너
        const refreshClientStatusBtn = document.getElementById('refreshClientStatus');
        if (refreshClientStatusBtn) {
            refreshClientStatusBtn.addEventListener('click', () => this.refreshClientStatus());
        }

        const refreshToolsBtn = document.getElementById('refreshTools');
        if (refreshToolsBtn) {
            refreshToolsBtn.addEventListener('click', () => this.refreshMcpTools());
        }

        // Claude Desktop 호환 관련 이벤트 리스너
        const exportClaudeConfigBtn = document.getElementById('exportClaudeConfig');
        if (exportClaudeConfigBtn) {
            exportClaudeConfigBtn.addEventListener('click', () => this.exportClaudeConfig());
        }

        const importClaudeConfigBtn = document.getElementById('importClaudeConfig');
        if (importClaudeConfigBtn) {
            importClaudeConfigBtn.addEventListener('click', () => this.importClaudeConfig());
        }

        // Advanced settings
        const exportBtn = document.getElementById('exportSettings');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportSettings());
        }

        const importBtn = document.getElementById('importSettings');
        if (importBtn) {
            importBtn.addEventListener('click', () => this.importSettings());
        }

        const resetBtn = document.getElementById('resetSettings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetSettings());
        }

        // API 타입 변경 시 UI 업데이트
        this.updateApiTypeUI();

        console.log('Event listeners setup completed');
    }

    setupFormListeners() {
        // LLM 모델 선택 리스너는 initializeLlmModelSelect에서 처리됨
        console.log('LLM model select listener will be attached in initializeLlmModelSelect');

        // API 유형 변경 리스너 (숨겨진 필드, llmModelSelect에서 자동 업데이트)
        const apiTypeSelect = document.getElementById('apiType');
        if (apiTypeSelect) {
            // apiType 필드를 숨김
            const apiTypeGroup = apiTypeSelect.closest('.form-group');
            if (apiTypeGroup) {
                apiTypeGroup.style.display = 'none';
            }

            apiTypeSelect.addEventListener('change', (e) => {
                console.log('API type changed to:', e.target.value);
                this.updateApiTypeUI();
                this.updateConfig();
                this.onLlmConfigChanged();
            });
        }
        
        // API 키 표시/숨기기 토글 버튼
        const toggleApiKeyBtn = document.getElementById('toggleApiKey');
        if (toggleApiKeyBtn) {
            toggleApiKeyBtn.addEventListener('click', () => this.toggleApiKeyVisibility());
        }

        // 모델 선택 드롭다운
        const modelSelect = document.getElementById('modelSelect');
        if (modelSelect) {
            modelSelect.addEventListener('change', (e) => {
                const selectedModel = e.target.value;
                const modelNameInput = document.getElementById('modelName');
                if (selectedModel && modelNameInput) {
                    // 모델명 입력 필드에 선택한 모델 자동 입력
                    modelNameInput.value = selectedModel;
                    console.log('Model selected from dropdown:', selectedModel);
                    console.log('Model name input automatically updated to:', selectedModel);

                    // 시각적 피드백
                    modelNameInput.style.transition = 'all 0.3s ease';
                    modelNameInput.style.backgroundColor = 'var(--accent-color-light)';
                    setTimeout(() => {
                        modelNameInput.style.backgroundColor = '';
                    }, 1000);

                    // 설정 업데이트
                    this.updateConfig();
                    this.onLlmConfigChanged();
                }
            });
        }

        // 모델명 직접 입력 필드
        const modelNameInput = document.getElementById('modelName');
        if (modelNameInput) {
            modelNameInput.addEventListener('change', (e) => {
                const selectedModel = e.target.value;
                if (selectedModel) {
                    console.log('Model entered manually:', selectedModel);
                    this.updateModelSettings(selectedModel);
                    this.updateConfig();
                    this.onLlmConfigChanged();
                }
            });

            // 입력 중에도 실시간으로 반영 (디바운스 적용)
            modelNameInput.addEventListener('input', (e) => {
                const selectedModel = e.target.value;
                if (selectedModel && selectedModel.length > 2) {
                    clearTimeout(this.modelInputTimeout);
                    this.modelInputTimeout = setTimeout(() => {
                        console.log('Model input changed:', selectedModel);
                        this.updateModelSettings(selectedModel);
                        this.updateConfig();
                    }, 500);
                }
            });
        }

        // 모델 목록 새로고침 버튼
        const refreshModelsBtn = document.getElementById('refreshModels');
        if (refreshModelsBtn) {
            refreshModelsBtn.addEventListener('click', () => this.refreshModelsList());
        }

        // API 연결 테스트 버튼
        const testConnectionBtn = document.getElementById('testConnection');
        if (testConnectionBtn) {
            testConnectionBtn.addEventListener('click', () => this.testLLMConnection());
        }
        
        // 언어 변경 이벤트 리스너 개선
        const languageSelect = document.getElementById('language');
        if (languageSelect) {
            languageSelect.addEventListener('change', async () => {
                const newLanguage = languageSelect.value;
                console.log(`Language changed to: ${newLanguage}`);
                
                // 언어 변경 이벤트 발생
                try {
                    const result = await window.electronAPI.changeLanguage(newLanguage);
                    if (result.success) {
                        // 직접 언어 적용 메서드 호출
                        this.applyLanguage(newLanguage);
                        this.updateConfig();
                    } else {
                        console.error('Failed to change language:', result.error);
                    }
                } catch (error) {
                    console.error('Error changing language:', error);
                }
            });
        }
        
        // 기존 이벤트 리스너 유지
        // General settings
        const generalInputs = ['startAtLogin', 'minimizeToTray'];
        generalInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.updateConfig());
                // input 이벤트도 추가하여 즉시 반영
                if (element.type !== 'checkbox') {
                    element.addEventListener('input', () => this.updateConfig());
                }
            }
        });

        // LLM settings
        const llmInputs = ['apiUrl', 'apiKey', 'customApiUrl', 'modelName', 'temperature', 'maxTokens'];
        llmInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => {
                    this.updateConfig();
                    // LLM 설정 변경 시 즉시 반영
                    this.onLlmConfigChanged();
                });
                element.addEventListener('input', () => {
                    this.updateConfig();
                    // 입력 중에도 설정 반영 (디바운스 적용)
                    this.debouncedLlmConfigUpdate();
                });
            }
        });

        // 프롬프트 설정
        const promptInputs = ['systemPrompt', 'userPromptTemplate', 'conversationStarters', 'enableSystemPrompt', 'enablePromptTemplate'];
        promptInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.updateConfig());
                element.addEventListener('input', () => this.updateConfig());
            }
        });

        // API 타입 변경 시 특별 처리 (이미 위에서 설정됨)

        // Appearance settings
        const appearanceInputs = ['theme', 'fontSize', 'fontFamily'];
        appearanceInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.updateConfig());
                if (element.type !== 'checkbox') {
                    element.addEventListener('input', () => this.updateConfig());
                }
            }
        });
        
        // Advanced settings
        const advancedInputs = ['enableDevTools', 'enableLogging'];
        advancedInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.updateConfig());
            }
        });
    }

    showSection(sectionId) {
        console.log(`Showing section: ${sectionId}`);

        try {
            // Hide all sections
            const allSections = document.querySelectorAll('.settings-section');
            console.log(`Found ${allSections.length} sections`);

            allSections.forEach(section => {
                section.classList.remove('active');
                section.style.display = 'none'; // 명시적으로 숨기기
            });

            // Remove active class from all nav buttons
            const allNavButtons = document.querySelectorAll('.settings-nav-btn');
            console.log(`Found ${allNavButtons.length} nav buttons`);

            allNavButtons.forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected section
            const targetSection = document.getElementById(sectionId);
            const targetButton = document.querySelector(`[data-section="${sectionId}"]`);

            if (targetSection) {
                targetSection.classList.add('active');
                targetSection.style.display = 'block'; // 명시적으로 표시
                console.log(`Section ${sectionId} shown`);

                // MCP 섹션이 표시될 때 현재 메모리의 서버 목록만 렌더링 (설정 파일 재로드 안함)
                if (sectionId === 'mcp') {
                    console.log('MCP section activated, rendering current server list...');
                    console.log('Current mcpServers in memory:', this.mcpServers);
                    console.log('Current mcpServers count:', this.mcpServers?.size || 0);

                    // 메모리에 있는 서버 목록만 렌더링 (설정 파일 재로드 안함)
                    setTimeout(() => {
                        this.renderMCPServers();
                        this.checkMcpServerStatus();
                        // 새로운 MCP 기능들도 초기화
                        this.refreshClientStatus();
                        this.refreshMcpTools();
                    }, 100);
                }
            } else {
                console.error(`Section ${sectionId} not found`);
            }

            if (targetButton) {
                targetButton.classList.add('active');
                console.log(`Button for ${sectionId} activated`);
            } else {
                console.error(`Button for ${sectionId} not found`);
            }

        } catch (error) {
            console.error('Error in showSection:', error);
        }
    }

    async populateForm() {
        if (!this.config) return;

        // General settings
        document.getElementById('language').value = this.config.language || 'ko';
        document.getElementById('startAtLogin').checked = this.config.general?.startAtLogin || false;
        document.getElementById('minimizeToTray').checked = this.config.general?.minimizeToTray || true;

        // LLM settings
        const apiType = this.config.llmConfig?.apiType || 'anthropic';

        // LLM 모델 선택 드롭다운 설정
        const llmModelSelect = document.getElementById('llmModelSelect');
        if (llmModelSelect) {
            llmModelSelect.value = apiType;
        }

        document.getElementById('apiType').value = apiType;
        document.getElementById('apiUrl').value = this.config.llmConfig?.apiUrl || 'https://api.anthropic.com/v1/messages';
        document.getElementById('modelName').value = this.config.llmConfig?.model || 'claude-3-5-sonnet-20241022';
        document.getElementById('temperature').value = this.config.llmConfig?.temperature || 0.7;
        document.getElementById('temperatureValue').textContent = this.config.llmConfig?.temperature || 0.7;
        document.getElementById('maxTokens').value = this.config.llmConfig?.maxTokens || 8192;
        document.getElementById('customApiUrl').value = this.config.llmConfig?.customApiUrl || '';

        console.log('LLM settings loaded:', {
            apiType,
            apiUrl: this.config.llmConfig?.apiUrl,
            model: this.config.llmConfig?.model,
            temperature: this.config.llmConfig?.temperature,
            maxTokens: this.config.llmConfig?.maxTokens
        });

        // 초기 UI 업데이트 (설정 로드 후)
        setTimeout(() => {
            this.updateApiTypeUI();
            // LLM 모델 선택 드롭다운도 초기화
            this.initializeLlmModelSelect();
        }, 100);

        // API 키 로드 (API 타입에 따라)
        try {
            const apiType = this.config.llmConfig?.apiType || 'local';
            let apiKey = '';

            if (apiType === 'openai' || apiType === 'anthropic' || apiType === 'custom') {
                // 1. 로컬 스토리지에서 확인
                const storageKey = apiType === 'openai' ? 'openai-api-key' :
                                 apiType === 'anthropic' ? 'anthropic-api-key' :
                                 'custom-api-key';
                const localApiKey = localStorage.getItem(storageKey);

                if (localApiKey) {
                    apiKey = localApiKey;
                    console.log(`API key loaded from localStorage (${storageKey})`);
                } else {
                    // 2. 직접 저장에서 확인
                    if (this.config.llmConfig?.directApiKey) {
                        apiKey = this.config.llmConfig.directApiKey;
                        console.log('API key loaded from config.llmConfig.directApiKey');
                        // 로컬 스토리지에도 저장
                        localStorage.setItem(storageKey, apiKey);
                    } else {
                        // 3. 암호화 저장소에서 확인
                        try {
                            if (window.electronAPI && window.electronAPI.getApiKey) {
                                const apiKeyResult = await window.electronAPI.getApiKey();
                                if (apiKeyResult?.success && apiKeyResult.apiKey) {
                                    apiKey = apiKeyResult.apiKey;
                                    console.log('API key loaded from encrypted storage');
                                    // 로컬 스토리지에도 저장
                                    localStorage.setItem(storageKey, apiKey);
                                }
                            }
                        } catch (encryptError) {
                            console.warn('Failed to load API key from encrypted storage:', encryptError);
                        }
                    }
                }

                // API 키 필드에 설정
                if (apiKey) {
                    document.getElementById('apiKey').value = apiKey;
                    console.log(`${apiType.toUpperCase()} API key loaded successfully`);
                }
            }
        } catch (error) {
            console.error('Failed to load API key:', error);
        }

        // Appearance settings
        document.getElementById('theme').value = this.config.theme || 'dark';
        document.getElementById('fontSize').value = this.config.appearance?.fontSize || '14';
        document.getElementById('fontFamily').value = this.config.appearance?.fontFamily || 'system';

        // Advanced settings
        document.getElementById('enableDevTools').checked = this.config.advanced?.enableDevTools || false;
        document.getElementById('enableLogging').checked = this.config.advanced?.enableLogging || false;

        // 프롬프트 설정
        const promptSettings = this.config.llmConfig?.promptSettings || {};
        document.getElementById('systemPrompt').value = promptSettings.systemPrompt || '당신은 도움이 되고 정확한 AI 어시스턴트입니다. 사용자의 질문에 친절하고 상세하게 답변해주세요.';
        document.getElementById('userPromptTemplate').value = promptSettings.userPromptTemplate || '{user_input}';

        // 대화 시작 문구 배열을 문자열로 변환
        const conversationStarters = Array.isArray(promptSettings.conversationStarters)
            ? promptSettings.conversationStarters.join('\n')
            : (promptSettings.conversationStarters || '안녕하세요! 무엇을 도와드릴까요?\n오늘 어떤 것에 대해 이야기하고 싶으신가요?\n궁금한 것이 있으시면 언제든 물어보세요!');
        document.getElementById('conversationStarters').value = conversationStarters;

        document.getElementById('enableSystemPrompt').checked = promptSettings.enableSystemPrompt !== false;
        document.getElementById('enablePromptTemplate').checked = promptSettings.enablePromptTemplate || false;

        // API 타입에 따른 UI 업데이트
        this.updateApiTypeUI();

        console.log('Settings loaded and UI updated');
    }

    // 모델 선택 시 설정 업데이트
    updateModelSettings(modelName) {
        try {
            console.log('Updating settings for model:', modelName);

            const modelConfigs = this.getModelConfigs();
            const modelConfig = modelConfigs[modelName];

            if (!modelConfig) {
                console.log('No specific config found for model:', modelName);
                return;
            }

            // API 타입 업데이트
            const apiTypeSelect = document.getElementById('apiType');
            if (apiTypeSelect && apiTypeSelect.value !== modelConfig.apiType) {
                apiTypeSelect.value = modelConfig.apiType;
                console.log('Updated API type to:', modelConfig.apiType);
            }

            // API URL 업데이트
            const apiUrlInput = document.getElementById('apiUrl');
            if (apiUrlInput) {
                apiUrlInput.value = modelConfig.apiUrl;
                apiUrlInput.placeholder = modelConfig.apiUrl;
                console.log('Updated API URL to:', modelConfig.apiUrl);
            }

            // API 키 필드 업데이트
            const apiKeyContainer = document.getElementById('apiKeyContainer');
            const apiKeyInput = document.getElementById('apiKey');
            if (apiKeyContainer && apiKeyInput) {
                if (modelConfig.apiKeyRequired) {
                    apiKeyContainer.style.display = 'block';
                    apiKeyInput.required = true;
                    apiKeyInput.placeholder = modelConfig.apiKeyHint;
                } else {
                    apiKeyContainer.style.display = 'none';
                    apiKeyInput.required = false;
                }
            }

            // 모델 선택 드롭다운에서 해당 모델 선택
            const modelSelect = document.getElementById('modelSelect');
            if (modelSelect) {
                // 드롭다운에서 해당 모델 찾아서 선택
                const options = modelSelect.querySelectorAll('option');
                let found = false;
                options.forEach(option => {
                    if (option.value === modelName) {
                        option.selected = true;
                        found = true;
                    } else {
                        option.selected = false;
                    }
                });

                if (!found) {
                    // 드롭다운에 없는 모델인 경우 첫 번째 옵션 선택
                    modelSelect.selectedIndex = 0;
                }
                console.log('Updated model dropdown selection:', modelName, 'found:', found);
            }

            // 모델명 입력 필드 업데이트
            const modelNameInput = document.getElementById('modelName');
            if (modelNameInput) {
                modelNameInput.value = modelName;
            }

            // 프롬프트 설정 업데이트
            if (modelConfig.promptSettings) {
                const systemPromptInput = document.getElementById('systemPrompt');
                const temperatureInput = document.getElementById('temperature');
                const maxTokensInput = document.getElementById('maxTokens');

                if (systemPromptInput && !systemPromptInput.value) {
                    systemPromptInput.value = modelConfig.promptSettings.systemPrompt;
                }
                if (temperatureInput) {
                    temperatureInput.value = modelConfig.promptSettings.temperature;
                }
                if (maxTokensInput) {
                    maxTokensInput.value = modelConfig.promptSettings.maxTokens;
                }
                console.log('Updated prompt settings:', modelConfig.promptSettings);
            }

            // 힌트 텍스트 업데이트
            const apiUrlHint = document.querySelector('.api-url-hint');
            const apiKeyHint = document.querySelector('.api-key-hint');
            const modelHint = document.querySelector('.model-hint');

            if (apiUrlHint) apiUrlHint.textContent = modelConfig.apiUrlHint;
            if (apiKeyHint) apiKeyHint.textContent = modelConfig.apiKeyHint;
            if (modelHint) modelHint.textContent = modelConfig.modelHint;

            // API 타입 UI도 업데이트
            this.updateApiTypeUI();

            console.log('Model settings updated successfully for:', modelName);

        } catch (error) {
            console.error('Error updating model settings:', error);
        }
    }

    // 모델별 설정 정보
    getModelConfigs() {
        return {
            // OpenAI 모델들
            'gpt-4': {
                apiType: 'openai',
                apiUrl: 'https://api.openai.com',
                apiKeyRequired: true,
                apiKeyHint: 'OpenAI API 키를 입력하세요 (sk-로 시작)',
                apiUrlHint: 'OpenAI API 서버 URL',
                modelHint: 'GPT-4 모델 - 가장 강력한 언어 모델',
                availableModels: ['gpt-4', 'gpt-4-turbo', 'gpt-4-turbo-preview'],
                promptSettings: {
                    systemPrompt: 'You are a helpful assistant.',
                    temperature: 0.7,
                    maxTokens: 4096
                }
            },
            'gpt-4-turbo': {
                apiType: 'openai',
                apiUrl: 'https://api.openai.com',
                apiKeyRequired: true,
                apiKeyHint: 'OpenAI API 키를 입력하세요 (sk-로 시작)',
                apiUrlHint: 'OpenAI API 서버 URL',
                modelHint: 'GPT-4 Turbo - 빠르고 효율적인 GPT-4',
                availableModels: ['gpt-4-turbo', 'gpt-4-turbo-preview', 'gpt-4'],
                promptSettings: {
                    systemPrompt: 'You are a helpful assistant.',
                    temperature: 0.7,
                    maxTokens: 4096
                }
            },
            'gpt-3.5-turbo': {
                apiType: 'openai',
                apiUrl: 'https://api.openai.com',
                apiKeyRequired: true,
                apiKeyHint: 'OpenAI API 키를 입력하세요 (sk-로 시작)',
                apiUrlHint: 'OpenAI API 서버 URL',
                modelHint: 'GPT-3.5 Turbo - 빠르고 경제적인 모델',
                availableModels: ['gpt-3.5-turbo', 'gpt-3.5-turbo-16k'],
                promptSettings: {
                    systemPrompt: 'You are a helpful assistant.',
                    temperature: 0.7,
                    maxTokens: 2048
                }
            },
            // Anthropic 모델들
            'claude-3-sonnet-20240229': {
                apiType: 'anthropic',
                apiUrl: 'https://api.anthropic.com',
                apiKeyRequired: true,
                apiKeyHint: 'Anthropic API 키를 입력하세요',
                apiUrlHint: 'Anthropic API 서버 URL',
                modelHint: 'Claude 3 Sonnet - 균형잡힌 성능의 Claude 모델',
                availableModels: ['claude-3-sonnet-20240229', 'claude-3-opus-20240229', 'claude-3-haiku-20240307'],
                promptSettings: {
                    systemPrompt: 'You are Claude, an AI assistant created by Anthropic.',
                    temperature: 0.7,
                    maxTokens: 4096
                }
            },
            'claude-3-opus-20240229': {
                apiType: 'anthropic',
                apiUrl: 'https://api.anthropic.com',
                apiKeyRequired: true,
                apiKeyHint: 'Anthropic API 키를 입력하세요',
                apiUrlHint: 'Anthropic API 서버 URL',
                modelHint: 'Claude 3 Opus - 가장 강력한 Claude 모델',
                availableModels: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
                promptSettings: {
                    systemPrompt: 'You are Claude, an AI assistant created by Anthropic.',
                    temperature: 0.7,
                    maxTokens: 4096
                }
            },
            // Ollama 모델들
            'llama2': {
                apiType: 'ollama',
                apiUrl: 'http://localhost:11434',
                apiKeyRequired: false,
                apiKeyHint: 'Ollama는 API 키가 필요하지 않습니다',
                apiUrlHint: 'Ollama 서버 URL (기본: localhost:11434)',
                modelHint: 'Llama 2 - Meta의 오픈소스 언어 모델',
                availableModels: ['llama2', 'llama2:13b', 'llama2:70b', 'codellama'],
                promptSettings: {
                    systemPrompt: 'You are a helpful assistant.',
                    temperature: 0.8,
                    maxTokens: 2048
                }
            },
            'codellama': {
                apiType: 'ollama',
                apiUrl: 'http://localhost:11434',
                apiKeyRequired: false,
                apiKeyHint: 'Ollama는 API 키가 필요하지 않습니다',
                apiUrlHint: 'Ollama 서버 URL (기본: localhost:11434)',
                modelHint: 'Code Llama - 코드 생성에 특화된 모델',
                availableModels: ['codellama', 'codellama:13b', 'codellama:34b', 'llama2'],
                promptSettings: {
                    systemPrompt: 'You are a helpful coding assistant.',
                    temperature: 0.3,
                    maxTokens: 2048
                }
            },
            'mistral': {
                apiType: 'ollama',
                apiUrl: 'http://localhost:11434',
                apiKeyRequired: false,
                apiKeyHint: 'Ollama는 API 키가 필요하지 않습니다',
                apiUrlHint: 'Ollama 서버 URL (기본: localhost:11434)',
                modelHint: 'Mistral - 효율적이고 강력한 오픈소스 모델',
                availableModels: ['mistral', 'mistral:7b', 'mixtral', 'llama2'],
                promptSettings: {
                    systemPrompt: 'You are a helpful assistant.',
                    temperature: 0.7,
                    maxTokens: 2048
                }
            }
        };
    }

    // API 타입별 올바른 URL을 반환하는 헬퍼 메서드
    getCorrectApiUrl(apiType, currentApiUrl, customApiUrl) {
        switch (apiType) {
            case 'openai':
                return 'https://api.openai.com';
            case 'anthropic':
                return 'https://api.anthropic.com';
            case 'ollama':
            case 'local':
                return currentApiUrl || 'http://localhost:11434';
            case 'llamacpp':
            case 'llama':
                return currentApiUrl || 'http://localhost:8080';
            case 'custom':
                return customApiUrl || currentApiUrl || 'https://api.openai.com';
            default:
                return currentApiUrl || 'https://api.openai.com';
        }
    }

    updateConfig() {
        // 이미 tempConfig가 없으면 현재 config 복사
        if (!this.tempConfig) {
            this.tempConfig = JSON.parse(JSON.stringify(this.config || {}));
        }

        try {
            // 현재 폼 값 수집
            const formValues = {
                language: document.getElementById('language')?.value,
                theme: document.getElementById('theme')?.value,
                general: {
                    startAtLogin: document.getElementById('startAtLogin')?.checked,
                    minimizeToTray: document.getElementById('minimizeToTray')?.checked
                },
                llmConfig: {
                    apiType: document.getElementById('apiType')?.value || document.getElementById('llmModelSelect')?.value || 'anthropic',
                    apiUrl: this.getCorrectApiUrl(
                        document.getElementById('apiType')?.value || document.getElementById('llmModelSelect')?.value,
                        document.getElementById('apiUrl')?.value,
                        document.getElementById('customApiUrl')?.value
                    ),
                    customApiUrl: document.getElementById('customApiUrl')?.value,
                    model: document.getElementById('modelName')?.value,
                    temperature: parseFloat(document.getElementById('temperature')?.value || 0.7),
                    maxTokens: parseInt(document.getElementById('maxTokens')?.value || 2048),
                    promptSettings: {
                        systemPrompt: document.getElementById('systemPrompt')?.value || '',
                        userPromptTemplate: document.getElementById('userPromptTemplate')?.value || '{user_input}',
                        conversationStarters: document.getElementById('conversationStarters')?.value.split('\n').filter(line => line.trim()) || [],
                        enableSystemPrompt: document.getElementById('enableSystemPrompt')?.checked || false,
                        enablePromptTemplate: document.getElementById('enablePromptTemplate')?.checked || false
                    }
                },
                appearance: {
                    fontSize: document.getElementById('fontSize')?.value,
                    fontFamily: document.getElementById('fontFamily')?.value
                },
                advanced: {
                    enableDevTools: document.getElementById('enableDevTools')?.checked,
                    enableLogging: document.getElementById('enableLogging')?.checked
                }
            };

            // 임시 설정 객체 업데이트
            this.tempConfig = {
                ...this.tempConfig,
                ...formValues
            };

            console.log('Updated tempConfig:', this.tempConfig);

            // 즉시 UI에 반영
            this.applyTempSettings();
        } catch (error) {
            console.error('Error updating config:', error);
        }
    }

    // 임시 설정을 UI에 즉시 반영하는 메서드
    applyTempSettings() {
        try {
            // 테마 변경 즉시 적용
            if (this.tempConfig.theme) {
                document.documentElement.setAttribute('data-theme', this.tempConfig.theme);
                console.log('Applied theme:', this.tempConfig.theme);
            }

            // 글꼴 크기 즉시 적용
            if (this.tempConfig.appearance?.fontSize) {
                document.documentElement.style.setProperty('--font-size-base', `${this.tempConfig.appearance.fontSize}px`);
                console.log('Applied font size:', this.tempConfig.appearance.fontSize);
            }

            // 글꼴 패밀리 즉시 적용
            if (this.tempConfig.appearance?.fontFamily) {
                let fontFamily = this.tempConfig.appearance.fontFamily;
                if (fontFamily === 'system') {
                    fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';
                }
                document.documentElement.style.setProperty('--font-family', fontFamily);
                console.log('Applied font family:', fontFamily);
            }

            // 언어 변경 즉시 적용 (이미 언어 변경 이벤트 리스너에서 처리됨)
        } catch (error) {
            console.error('Error applying temp settings:', error);
        }
    }

    async saveSettings() {
        try {
            // API 키 저장 (여러 방법으로 시도)
            const apiKey = document.getElementById('apiKey').value;
            const apiType = document.getElementById('apiType').value;

            if (apiKey && (apiType === 'openai' || apiType === 'anthropic' || apiType === 'custom')) {
                try {
                    console.log(`Saving ${apiType} API key...`);

                    // 1. 로컬 스토리지에 저장 (가장 확실한 방법)
                    const storageKey = apiType === 'openai' ? 'openai-api-key' :
                                     apiType === 'anthropic' ? 'anthropic-api-key' :
                                     'custom-api-key';
                    localStorage.setItem(storageKey, apiKey);
                    console.log(`API key saved to localStorage with key: ${storageKey}`);

                    // 2. 직접 저장 (설정 객체에 포함)
                    if (!this.config.llmConfig) this.config.llmConfig = {};
                    this.config.llmConfig.directApiKey = apiKey;
                    console.log('API key saved to config.llmConfig.directApiKey');

                    // 3. 암호화 저장 시도 (선택사항)
                    try {
                        if (window.electronAPI && window.electronAPI.saveApiKey) {
                            const result = await window.electronAPI.saveApiKey(apiKey);
                            console.log('Encrypted API key save result:', result);
                        }
                    } catch (encryptError) {
                        console.warn('Failed to save API key with encryption (continuing):', encryptError);
                    }

                    this.showNotification(`${apiType.toUpperCase()} API 키가 저장되었습니다.`, 'success');
                } catch (apiKeyError) {
                    console.error('Failed to save API key:', apiKeyError);
                    this.showNotification('API 키 저장에 실패했습니다.', 'error');
                    throw apiKeyError; // 저장 실패 시 전체 저장 중단
                }
            } else if ((apiType === 'openai' || apiType === 'anthropic') && !apiKey) {
                this.showNotification(`${apiType.toUpperCase()} API 키를 입력해주세요.`, 'error');
                throw new Error(`${apiType.toUpperCase()} API 키가 필요합니다.`);
            }

            // 임시 설정 객체가 있으면 사용, 없으면 폼에서 수집
            let newConfig;
            if (this.tempConfig) {
                newConfig = {
                    ...this.config,
                    ...this.tempConfig,
                    mcpServers: Object.fromEntries(this.mcpServers)
                };
            } else {
                // 기존 코드 (폼에서 직접 수집)
                newConfig = {
                    ...this.config,
                    language: document.getElementById('language').value,
                    theme: document.getElementById('theme').value,
                    general: {
                        startAtLogin: document.getElementById('startAtLogin').checked,
                        minimizeToTray: document.getElementById('minimizeToTray').checked
                    },
                    llmConfig: {
                        apiType: document.getElementById('apiType')?.value || document.getElementById('llmModelSelect')?.value || 'anthropic',
                        apiUrl: this.getCorrectApiUrl(
                            document.getElementById('apiType')?.value || document.getElementById('llmModelSelect')?.value,
                            document.getElementById('apiUrl')?.value,
                            document.getElementById('customApiUrl')?.value
                        ),
                        customApiUrl: document.getElementById('customApiUrl')?.value || '',
                        model: document.getElementById('modelName')?.value || '',
                        temperature: parseFloat(document.getElementById('temperature')?.value || 0.7),
                        maxTokens: parseInt(document.getElementById('maxTokens')?.value || 2048),
                        promptSettings: {
                            systemPrompt: document.getElementById('systemPrompt')?.value || '',
                            userPromptTemplate: document.getElementById('userPromptTemplate')?.value || '{user_input}',
                            conversationStarters: document.getElementById('conversationStarters')?.value?.split('\n').filter(line => line.trim()) || [],
                            enableSystemPrompt: document.getElementById('enableSystemPrompt')?.checked || false,
                            enablePromptTemplate: document.getElementById('enablePromptTemplate')?.checked || false
                        }
                    },
                    appearance: {
                        fontSize: document.getElementById('fontSize').value,
                        fontFamily: document.getElementById('fontFamily').value
                    },
                    advanced: {
                        enableDevTools: document.getElementById('enableDevTools').checked,
                        enableLogging: document.getElementById('enableLogging').checked
                    },
                    mcpServers: Object.fromEntries(this.mcpServers)
                };
                
                // API 키를 직접 설정 객체에 포함 (암호화 없이)
                if (apiKey) {
                    if (!newConfig.llmConfig) newConfig.llmConfig = {};
                    newConfig.llmConfig.directApiKey = apiKey;
                }
            }

            // Save config - 안전한 저장 방식
            console.log('Saving config:', newConfig);

            let result = null;

            // 1. electronAPI를 통한 저장 시도 (iframe 환경 고려)
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (parentElectronAPI && parentElectronAPI.saveConfig) {
                console.log('Saving via electronAPI...');
                result = await parentElectronAPI.saveConfig(newConfig);
                console.log('ElectronAPI save result:', result);
            } else {
                console.log('ElectronAPI not available, using localStorage fallback...');

                // 2. localStorage 백업 저장
                try {
                    localStorage.setItem('mcpDesktopConfig', JSON.stringify(newConfig));
                    console.log('Config saved to localStorage successfully');

                    // 성공 결과 시뮬레이션
                    result = { success: true, message: 'Config saved to localStorage' };
                } catch (storageError) {
                    console.error('Failed to save to localStorage:', storageError);
                    throw new Error('설정 저장에 실패했습니다: ' + storageError.message);
                }
            }
            
            if (result && result.success) {
                this.config = newConfig;
                this.tempConfig = null; // 임시 설정 객체 초기화

                // Show success message
                this.showNotification('설정이 저장되었습니다.', 'success');
                
                // 설정 저장 후 설정 화면만 닫기 (전체 창 닫기 대신)
                console.log('Closing settings panel only...');
                this.closeSettingsPanel();
            } else {
                throw new Error(result?.error || '알 수 없는 오류');
            }
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showNotification('설정 저장에 실패했습니다: ' + error.message, 'error');
        }
    }

    // 설정 패널만 닫는 새로운 메서드
    closeSettingsPanel() {
        console.log('Closing settings panel only...');
        
        // 모달인 경우 (DOM 조작)
        const settingsModal = document.getElementById('settings-modal');
        if (settingsModal) {
            settingsModal.classList.remove('active');
            console.log('Removed active class from settings modal');
        }
        
        // 독립 창이 아닌 경우 이벤트 발생
        if (window.electronAPI && window.electronAPI.hideSettingsPanel) {
            window.electronAPI.hideSettingsPanel();
        }
        
        // 메인 앱에 설정 저장 완료 이벤트 전송
        if (window.electronAPI && window.electronAPI.settingsSaved) {
            window.electronAPI.settingsSaved();
        }
    }

    async closeSettings() {
        console.log('Closing settings...');
        
        // 임시 설정이 있으면 원래 설정으로 되돌림
        if (this.tempConfig && this.config) {
            // 테마 되돌리기
            document.documentElement.setAttribute('data-theme', this.config.theme || 'dark');
            
            // 글꼴 크기 되돌리기
            const originalFontSize = this.config.appearance?.fontSize || '14';
            document.documentElement.style.setProperty('--font-size-base', `${originalFontSize}px`);
            
            // 글꼴 패밀리 되돌리기
            let originalFontFamily = this.config.appearance?.fontFamily || 'system';
            if (originalFontFamily === 'system') {
                originalFontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';
            }
            document.documentElement.style.setProperty('--font-family', originalFontFamily);
        }
        
        // 모달 또는 패널만 닫기
        this.closeSettingsPanel();
    }

    async testLLMConnection() {
        const button = document.getElementById('testConnection');
        if (!button) {
            console.error('Test connection button not found');
            return;
        }

        const originalText = button.textContent;
        button.textContent = '테스트 중...';
        button.disabled = true;

        try {
            // 현재 설정 수집 - null 체크 강화
            const apiTypeElement = document.getElementById('apiType');
            const apiUrlElement = document.getElementById('apiUrl');
            const apiKeyElement = document.getElementById('apiKey');
            const customApiUrlElement = document.getElementById('customApiUrl');
            const modelNameElement = document.getElementById('modelName');
            const temperatureElement = document.getElementById('temperature');
            const maxTokensElement = document.getElementById('maxTokens');

            if (!apiTypeElement || !apiUrlElement || !modelNameElement) {
                throw new Error('필수 설정 요소를 찾을 수 없습니다.');
            }

            // API 타입별 올바른 URL 설정
            const apiType = apiTypeElement.value || 'openai';
            let correctApiUrl = apiUrlElement.value;

            // API 타입별 기본 URL 강제 설정
            if (apiType === 'openai') {
                correctApiUrl = 'https://api.openai.com';
            } else if (apiType === 'anthropic') {
                correctApiUrl = 'https://api.anthropic.com';
            } else if (apiType === 'ollama' || apiType === 'local') {
                correctApiUrl = apiUrlElement.value || 'http://localhost:11434';
            } else if (apiType === 'llamacpp' || apiType === 'llama') {
                correctApiUrl = apiUrlElement.value || 'http://localhost:8080';
            } else if (apiType === 'custom') {
                correctApiUrl = customApiUrlElement ? customApiUrlElement.value : apiUrlElement.value;
            }

            const llmConfig = {
                apiType: apiType,
                apiUrl: correctApiUrl,
                apiKey: apiKeyElement ? apiKeyElement.value : '',
                customApiUrl: customApiUrlElement ? customApiUrlElement.value : '',
                model: modelNameElement.value || 'gpt-3.5-turbo',
                temperature: temperatureElement ? parseFloat(temperatureElement.value) || 0.7 : 0.7,
                maxTokens: maxTokensElement ? parseInt(maxTokensElement.value) || 2048 : 2048
            };

            console.log('Testing connection with config:', {
                ...llmConfig,
                apiKey: llmConfig.apiKey ? '***' : ''
            });

            // LLM 클라이언트 생성
            if (!window.LLMClient) {
                throw new Error('LLM 클라이언트를 찾을 수 없습니다.');
            }

            const llmClient = new window.LLMClient(llmConfig);

            // 연결 테스트
            const result = await llmClient.testConnection();

            if (result.success) {
                // 성공 시 버튼 텍스트를 잠시 변경
                if (button) {
                    button.textContent = '연결 성공';
                    button.style.backgroundColor = 'var(--success-color, #28a745)';
                    setTimeout(() => {
                        button.textContent = originalText;
                        button.style.backgroundColor = '';
                    }, 2000);
                }
                this.showNotification(result.message || '연결 테스트에 성공했습니다.', 'success');
            } else {
                throw new Error(result.error || '연결 테스트에 실패했습니다.');
            }
        } catch (error) {
            console.error('Connection test failed:', error);
            this.showNotification('연결 실패: ' + error.message, 'error');

            // 실패 시에만 버튼 복원
            if (button) {
                button.textContent = originalText;
                button.disabled = false;
            }
        } finally {
            // 성공 시에는 setTimeout에서 처리하므로 여기서는 disabled만 해제
            if (button && button.textContent === '테스트 중...') {
                button.textContent = originalText;
                button.disabled = false;
            } else if (button && button.textContent !== '연결 성공') {
                button.disabled = false;
            }
        }
    }

    showAddServerForm(serverName = null) {
        console.log('showAddServerForm called with serverName:', serverName);

        // 폼 표시
        const addServerForm = document.getElementById('addServerForm');
        const addServerBtn = document.getElementById('addServerBtn');

        if (addServerForm) {
            addServerForm.style.display = 'block';
        } else {
            console.error('addServerForm not found');
            return;
        }

        if (addServerBtn) {
            addServerBtn.style.display = 'none';
        }

        // 폼 초기화
        const serverNameInput = document.getElementById('serverName');
        const serverCommandInput = document.getElementById('serverCommand');
        const serverEnvInput = document.getElementById('serverEnv');

        if (serverNameInput) serverNameInput.value = '';
        if (serverCommandInput) serverCommandInput.value = 'npx -y @modelcontextprotocol/server-filesystem';
        if (serverEnvInput) serverEnvInput.value = '{}';

        // 허용 디렉토리 초기화
        this.allowedDirectories = [];
        this.renderAllowedDirectories();

        // 서버 편집 모드인 경우
        if (serverName && this.mcpServers.has(serverName)) {
            console.log('Editing existing server:', serverName);
            const serverConfig = this.mcpServers.get(serverName);

            if (serverNameInput) serverNameInput.value = serverName;

            // 전체 명령어 재구성
            if (serverConfig.command && serverConfig.args) {
                const fullCommand = [serverConfig.command, ...serverConfig.args].join(' ');
                if (serverCommandInput) serverCommandInput.value = fullCommand;

                // 허용 디렉토리 추출 (경로처럼 보이는 인수들)
                const directories = serverConfig.args.filter(arg =>
                    (arg.includes('/') || arg.includes('\\') || arg.includes(':')) &&
                    !arg.startsWith('-') && // 옵션이 아닌 것
                    !arg.includes('@') // 패키지명이 아닌 것
                );

                this.allowedDirectories = directories;
            } else {
                this.allowedDirectories = [];
            }

            this.renderAllowedDirectories();

            if (serverEnvInput) {
                serverEnvInput.value = JSON.stringify(serverConfig.env || {}, null, 2);
            }
        }

        // 허용 디렉토리 관련 리스너 설정
        this.setupDirectoryListeners();

        console.log('Form initialized for server:', serverName);
    }



    // 허용 디렉토리 관련 리스너 설정
    setupDirectoryListeners() {
        const addDirectoryBtn = document.getElementById('addDirectoryBtn');
        const browseDirectoryBtn = document.getElementById('browseDirectoryBtn');
        const newDirectoryPath = document.getElementById('newDirectoryPath');

        if (addDirectoryBtn) {
            addDirectoryBtn.addEventListener('click', () => this.addDirectory());
        }

        if (browseDirectoryBtn) {
            browseDirectoryBtn.addEventListener('click', () => this.browseDirectory());
        }

        if (newDirectoryPath) {
            newDirectoryPath.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addDirectory();
                }
            });
        }
    }

    // 디렉토리 추가
    addDirectory() {
        const newDirectoryPath = document.getElementById('newDirectoryPath');
        if (!newDirectoryPath) return;

        const path = newDirectoryPath.value.trim();
        if (!path) {
            this.showNotification('디렉토리 경로를 입력하세요.', 'error');
            return;
        }

        if (this.allowedDirectories.includes(path)) {
            this.showNotification('이미 추가된 디렉토리입니다.', 'error');
            return;
        }

        this.allowedDirectories.push(path);
        newDirectoryPath.value = '';
        this.renderAllowedDirectories();
    }

    // 디렉토리 찾아보기
    async browseDirectory() {
        try {
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (parentElectronAPI && parentElectronAPI.showOpenDialog) {
                const result = await parentElectronAPI.showOpenDialog({
                    title: '허용 디렉토리 선택',
                    properties: ['openDirectory']
                });

                if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
                    const selectedPath = result.filePaths[0];
                    if (!this.allowedDirectories.includes(selectedPath)) {
                        this.allowedDirectories.push(selectedPath);
                        this.renderAllowedDirectories();
                    } else {
                        this.showNotification('이미 추가된 디렉토리입니다.', 'error');
                    }
                }
            } else {
                this.showNotification('파일 탐색기를 사용할 수 없습니다.', 'error');
            }
        } catch (error) {
            console.error('Failed to browse directory:', error);
            this.showNotification('디렉토리 선택에 실패했습니다.', 'error');
        }
    }

    // 허용 디렉토리 목록 렌더링
    renderAllowedDirectories() {
        const container = document.getElementById('allowedDirectoriesList');
        if (!container) return;

        container.innerHTML = '';

        if (this.allowedDirectories.length === 0) {
            container.innerHTML = '<div class="empty-state">허용된 디렉토리가 없습니다.</div>';
            return;
        }

        this.allowedDirectories.forEach((directory, index) => {
            const directoryItem = document.createElement('div');
            directoryItem.className = 'directory-item';
            directoryItem.innerHTML = `
                <span class="directory-path">${directory}</span>
                <button type="button" class="btn btn-danger btn-sm" onclick="settingsManager.removeDirectory(${index})">제거</button>
            `;
            container.appendChild(directoryItem);
        });
    }

    // 디렉토리 제거
    removeDirectory(index) {
        if (index >= 0 && index < this.allowedDirectories.length) {
            this.allowedDirectories.splice(index, 1);
            this.renderAllowedDirectories();
        }
    }

    // MCP 클라이언트 상태 새로고침
    async refreshMcpClientStatus() {
        try {
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (parentElectronAPI && parentElectronAPI.mcpClientGetStatus) {
                const result = await parentElectronAPI.mcpClientGetStatus();
                if (result.success) {
                    this.renderMcpClientStatus(result.status);
                } else {
                    console.error('Failed to get MCP client status:', result.error);
                    this.showNotification('MCP 클라이언트 상태 확인에 실패했습니다.', 'error');
                }
            } else {
                console.warn('MCP client status API not available');
                this.showNotification('MCP 클라이언트 API를 사용할 수 없습니다.', 'error');
            }
        } catch (error) {
            console.error('Error refreshing MCP client status:', error);
            this.showNotification('MCP 클라이언트 상태 새로고침에 실패했습니다.', 'error');
        }
    }

    // MCP 클라이언트 상태 렌더링
    renderMcpClientStatus(status) {
        const container = document.getElementById('mcpClientStatus');
        if (!container) return;

        container.innerHTML = '';

        if (!status || Object.keys(status).length === 0) {
            container.innerHTML = '<div class="empty-state">연결된 MCP 클라이언트가 없습니다.</div>';
            return;
        }

        Object.entries(status).forEach(([serverName, serverStatus]) => {
            const statusItem = document.createElement('div');
            statusItem.className = 'client-status-item';

            const isConnected = serverStatus.connected;
            const statusClass = isConnected ? 'connected' : 'disconnected';
            const statusText = isConnected ? '연결됨' : '연결 끊김';

            statusItem.innerHTML = `
                <div class="status-header">
                    <span class="server-name">${serverName}</span>
                    <span class="status-indicator ${statusClass}">${statusText}</span>
                </div>
                <div class="status-details">
                    도구: ${serverStatus.toolsCount || 0}개,
                    리소스: ${serverStatus.resourcesCount || 0}개,
                    프롬프트: ${serverStatus.promptsCount || 0}개
                </div>
            `;

            container.appendChild(statusItem);
        });
    }

    // MCP 도구 새로고침
    async refreshMcpTools() {
        try {
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (parentElectronAPI && parentElectronAPI.mcpClientGetTools) {
                const result = await parentElectronAPI.mcpClientGetTools();
                if (result.success) {
                    this.renderMcpTools(result.tools);
                } else {
                    console.error('Failed to get MCP tools:', result.error);
                    this.showNotification('MCP 도구 목록 확인에 실패했습니다.', 'error');
                }
            } else {
                console.warn('MCP tools API not available');
                this.showNotification('MCP 도구 API를 사용할 수 없습니다.', 'error');
            }
        } catch (error) {
            console.error('Error refreshing MCP tools:', error);
            this.showNotification('MCP 도구 새로고침에 실패했습니다.', 'error');
        }
    }

    // MCP 도구 렌더링
    renderMcpTools(tools) {
        const container = document.getElementById('mcpToolsList');
        if (!container) return;

        container.innerHTML = '';

        if (!tools || Object.keys(tools).length === 0) {
            container.innerHTML = '<div class="empty-state">사용 가능한 도구가 없습니다.</div>';
            return;
        }

        Object.entries(tools).forEach(([serverName, serverTools]) => {
            if (serverTools && serverTools.length > 0) {
                serverTools.forEach(tool => {
                    const toolItem = document.createElement('div');
                    toolItem.className = 'tool-item';

                    toolItem.innerHTML = `
                        <div class="tool-header">
                            <span class="tool-name">${tool.name}</span>
                            <span class="tool-server">${serverName}</span>
                        </div>
                        <div class="tool-description">${tool.description || '설명 없음'}</div>
                        <button class="btn btn-secondary test-tool-btn" onclick="settingsManager.testTool('${tool.name}', '${serverName}')">
                            테스트
                        </button>
                    `;

                    container.appendChild(toolItem);
                });
            }
        });
    }

    // 도구 테스트
    async testTool(toolName, serverName) {
        try {
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (parentElectronAPI && parentElectronAPI.mcpClientCallTool) {
                this.showNotification(`도구 '${toolName}' 테스트 중...`, 'info');

                // 간단한 테스트 인수 (도구에 따라 다를 수 있음)
                const testArgs = {};

                const result = await parentElectronAPI.mcpClientCallTool(toolName, testArgs);
                if (result.success) {
                    this.showNotification(`도구 '${toolName}' 테스트 성공`, 'success');
                    console.log('Tool test result:', result.result);
                } else {
                    this.showNotification(`도구 '${toolName}' 테스트 실패: ${result.error}`, 'error');
                }
            } else {
                this.showNotification('MCP 도구 호출 API를 사용할 수 없습니다.', 'error');
            }
        } catch (error) {
            console.error('Error testing tool:', error);
            this.showNotification(`도구 테스트 중 오류 발생: ${error.message}`, 'error');
        }
    }

    // MCP 진단
    async diagnoseMcp() {
        try {
            console.log('🔍 Starting MCP diagnosis...');
            this.showNotification('MCP 진단을 시작합니다...', 'info');

            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
            if (!parentElectronAPI || !parentElectronAPI.mcpDiagnose) {
                this.showNotification('MCP 진단 API를 사용할 수 없습니다.', 'error');
                return;
            }

            const result = await parentElectronAPI.mcpDiagnose();

            if (result.success) {
                console.log('📊 MCP Diagnosis Results:', result.diagnosis);
                this.displayDiagnosisResults(result.diagnosis);
                this.showNotification('MCP 진단이 완료되었습니다.', 'success');
            } else {
                console.error('❌ MCP diagnosis failed:', result.error);
                this.showNotification(`MCP 진단 실패: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Error during MCP diagnosis:', error);
            this.showNotification(`MCP 진단 중 오류 발생: ${error.message}`, 'error');
        }
    }

    // 진단 결과 표시
    displayDiagnosisResults(diagnosis) {
        const container = document.getElementById('mcpClientStatus');
        if (!container) return;

        let html = '<div class="diagnosis-results">';
        html += '<h4>🔍 MCP 진단 결과</h4>';

        // 서버 프로세스 상태
        html += '<div class="diagnosis-section">';
        html += '<h5>📋 서버 프로세스</h5>';
        if (Object.keys(diagnosis.serverProcesses).length === 0) {
            html += '<p class="diagnosis-warning">실행 중인 MCP 서버 프로세스가 없습니다.</p>';
        } else {
            for (const [name, status] of Object.entries(diagnosis.serverProcesses)) {
                const statusIcon = status.running ? '✅' : '❌';
                const statusText = status.running ? '실행 중' : '중지됨';
                html += `<div class="diagnosis-item">
                    ${statusIcon} <strong>${name}</strong>: ${statusText} (PID: ${status.pid || 'N/A'})
                </div>`;
            }
        }
        html += '</div>';

        // 클라이언트 연결 상태
        html += '<div class="diagnosis-section">';
        html += '<h5>🔗 클라이언트 연결</h5>';
        if (Object.keys(diagnosis.clientConnections).length === 0) {
            html += '<p class="diagnosis-warning">연결된 MCP 클라이언트가 없습니다.</p>';
        } else {
            for (const [name, status] of Object.entries(diagnosis.clientConnections)) {
                const statusIcon = status.connected ? '✅' : '❌';
                const statusText = status.connected ? '연결됨' : '연결 끊김';
                html += `<div class="diagnosis-item">
                    ${statusIcon} <strong>${name}</strong>: ${statusText}
                    (도구: ${status.toolsCount || 0}개, 리소스: ${status.resourcesCount || 0}개)
                </div>`;
            }
        }
        html += '</div>';

        // 사용 가능한 도구
        html += '<div class="diagnosis-section">';
        html += '<h5>🔧 사용 가능한 도구</h5>';
        if (Object.keys(diagnosis.availableTools).length === 0) {
            html += '<p class="diagnosis-warning">사용 가능한 도구가 없습니다.</p>';
        } else {
            for (const [serverName, tools] of Object.entries(diagnosis.availableTools)) {
                html += `<div class="diagnosis-item">
                    🔧 <strong>${serverName}</strong>: ${tools.join(', ')}
                </div>`;
            }
        }
        html += '</div>';

        // 설정된 서버
        if (diagnosis.configuredServers) {
            html += '<div class="diagnosis-section">';
            html += '<h5>⚙️ 설정된 서버</h5>';
            html += `<div class="diagnosis-item">📄 ${diagnosis.configuredServers.join(', ')}</div>`;
            html += '</div>';
        }

        // 오류 목록
        if (diagnosis.errors && diagnosis.errors.length > 0) {
            html += '<div class="diagnosis-section">';
            html += '<h5>❌ 오류</h5>';
            for (const error of diagnosis.errors) {
                html += `<div class="diagnosis-error">⚠️ ${error}</div>`;
            }
            html += '</div>';
        }

        html += '</div>';
        container.innerHTML = html;
    }

    hideAddServerForm() {
        console.log('Hiding add server form');
        const form = document.getElementById('addServerForm');
        if (form) {
            form.style.display = 'none';
        } else {
            console.error('addServerForm element not found');
        }
        
        const addBtn = document.getElementById('addServerBtn');
        if (addBtn) {
            addBtn.style.display = 'block';
        } else {
            console.error('addServerBtn element not found');
        }
        
        this.clearServerForm();
    }

    clearServerForm() {
        const nameInput = document.getElementById('serverName');
        const commandInput = document.getElementById('serverCommand');
        const argsInput = document.getElementById('serverArgs');
        const envInput = document.getElementById('serverEnv');
        
        if (nameInput) nameInput.value = '';
        if (commandInput) commandInput.value = '';
        if (argsInput) argsInput.value = '';
        if (envInput) envInput.value = '{}';
    }

    async saveServer() {
        const name = document.getElementById('serverName').value.trim();
        const fullCommand = document.getElementById('serverCommand').value.trim();
        const env = document.getElementById('serverEnv').value.trim();

        if (!name || !fullCommand) {
            this.showNotification('서버 이름과 명령어는 필수입니다.', 'error');
            return;
        }

        if (!this.allowedDirectories || this.allowedDirectories.length === 0) {
            this.showNotification('최소 하나의 허용 디렉토리를 추가해야 합니다.', 'error');
            return;
        }

        try {
            // 환경 변수 JSON 파싱 검증
            let parsedEnv = {};
            if (env) {
                try {
                    parsedEnv = JSON.parse(env);
                } catch (jsonError) {
                    this.showNotification('환경 변수는 유효한 JSON 형식이어야 합니다.', 'error');
                    return;
                }
            }

            // 전체 명령어를 파싱하여 command와 args로 분리
            const commandParts = fullCommand.match(/(?:[^\s"]+|"[^"]*")+/g) || [];
            if (commandParts.length === 0) {
                this.showNotification('유효한 명령어를 입력하세요.', 'error');
                return;
            }

            const command = commandParts[0];
            let args = commandParts.slice(1);

            // 허용 디렉토리 추가
            args.push(...this.allowedDirectories);

            // 서버 설정 객체 생성 (Claude Desktop 표준 형식)
            const serverConfig = {
                command,
                args
            };

            // env가 있고 비어있지 않은 경우에만 추가
            if (parsedEnv && Object.keys(parsedEnv).length > 0) {
                serverConfig.env = parsedEnv;
            }

            console.log('Saving server:', name, serverConfig);

            // 기존 서버인지 확인 (편집 모드)
            const isEditing = this.mcpServers.has(name);

            // Map에 서버 추가/업데이트
            this.mcpServers.set(name, serverConfig);

            // MCP 설정을 별도 파일에 저장
            console.log('Saving MCP server to mcp_desktop_config.json...');
            await this.saveMcpConfigSafely(this.mcpServers);

            // 메인 설정도 업데이트 (호환성을 위해)
            const newConfig = { ...this.config };
            newConfig.mcpServers = Object.fromEntries(this.mcpServers);
            this.config = newConfig;

            console.log('Server saved successfully, updating UI...');

            // UI 업데이트
            this.renderMCPServers();
            this.updateServerStatusContainer(); // 상태 컨테이너도 업데이트
            this.hideAddServerForm();

            const message = isEditing ? 'MCP 서버가 수정되었습니다.' : 'MCP 서버가 추가되었습니다.';
            this.showNotification(message, 'success');

            console.log('MCP server save completed');
        } catch (error) {
            console.error('Failed to save MCP server:', error);
            this.showNotification('MCP 서버 저장에 실패했습니다: ' + error.message, 'error');
        }
    }

    editServer(name) {
        if (this.mcpServers.has(name)) {
            this.showAddServerForm(name);
        }
    }

    async removeServer(name) {
        if (confirm(`'${name}' 서버를 삭제하시겠습니까?`)) {
            try {
                // 실행 중인 서버라면 중지
                if (this.serverStatus[name]?.running) {
                    const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;
                    if (parentElectronAPI && parentElectronAPI.stopMcpServer) {
                        parentElectronAPI.stopMcpServer(name);
                    }
                }

                // Map에서 서버 제거
                this.mcpServers.delete(name);

                // MCP 설정을 별도 파일에 저장
                console.log('Saving updated MCP config after server deletion...');
                await this.saveMcpConfigSafely(this.mcpServers);

                // 메인 설정도 업데이트 (호환성을 위해)
                const newConfig = { ...this.config };
                newConfig.mcpServers = Object.fromEntries(this.mcpServers);
                this.config = newConfig;
                console.log('Local config updated after server deletion');

                // UI 업데이트
                this.renderMCPServers();
                this.updateServerStatusContainer(); // 상태 컨테이너도 업데이트
                this.showNotification('MCP 서버가 삭제되었습니다.', 'success');
            } catch (error) {
                console.error('Failed to remove MCP server:', error);
                this.showNotification('MCP 서버 삭제에 실패했습니다: ' + error.message, 'error');
            }
        }
    }

    async startServer(name) {
        try {
            const serverConfig = this.mcpServers.get(name);
            if (!serverConfig) {
                throw new Error(`서버 ${name}을(를) 찾을 수 없습니다.`);
            }

            console.log('Starting server:', name, serverConfig);

            // UI에서 즉시 시작 중 상태 표시
            this.updateServerStatusUI(name, true);

            // 부모 창의 electronAPI 확인 (iframe 환경)
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;

            if (parentElectronAPI && parentElectronAPI.startMcpServerWithConfig) {
                // 서버 시작 요청
                const result = await parentElectronAPI.startMcpServerWithConfig({
                    name,
                    command: serverConfig.command,
                    args: serverConfig.args || [],
                    env: serverConfig.env || {}
                });

                if (result.success) {
                    this.showNotification(`${name} 서버가 시작되었습니다.`, 'success');
                    // 서버 상태는 이미 업데이트됨
                } else {
                    // 실패 시 상태 되돌리기
                    this.updateServerStatusUI(name, false);
                    throw new Error(result.error || '알 수 없는 오류');
                }
            } else {
                // 웹 환경에서는 서버 시작 불가
                this.updateServerStatusUI(name, false);
                this.showNotification('웹 환경에서는 MCP 서버를 시작할 수 없습니다. 설정만 저장됩니다.', 'info');
            }
        } catch (error) {
            console.error('Failed to start MCP server:', error);
            this.showNotification(`서버 시작 실패: ${error.message}`, 'error');
            // 오류 시 상태 되돌리기
            this.updateServerStatusUI(name, false);
        }
    }

    async stopServer(name) {
        try {
            // UI에서 즉시 중지 중 상태 표시
            this.updateServerStatusUI(name, false);

            // 부모 창의 electronAPI 확인 (iframe 환경)
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;

            if (parentElectronAPI && parentElectronAPI.stopMcpServer) {
                const result = await parentElectronAPI.stopMcpServer(name);

                if (result.success) {
                    this.showNotification(`${name} 서버가 중지되었습니다.`, 'success');
                    // 서버 상태는 이미 업데이트됨
                } else {
                    // 실패 시 상태 되돌리기
                    this.updateServerStatusUI(name, true);
                    throw new Error(result.error || '알 수 없는 오류');
                }
            } else {
                // 웹 환경에서는 서버 중지 불가
                this.updateServerStatusUI(name, false);
                this.showNotification('웹 환경에서는 MCP 서버를 중지할 수 없습니다.', 'info');
            }
        } catch (error) {
            console.error('Failed to stop MCP server:', error);
            this.showNotification(`서버 중지 실패: ${error.message}`, 'error');
            // 오류 시 상태 되돌리기 (실행 중으로)
            this.updateServerStatusUI(name, true);
        }
    }

    setupMcpServerListeners() {
        // Add server button
        const addServerBtn = document.getElementById('addServerBtn');
        if (addServerBtn) {
            addServerBtn.addEventListener('click', () => this.showAddServerForm());
        }

        // Cancel add server button
        const cancelServerBtn = document.getElementById('cancelServerBtn');
        if (cancelServerBtn) {
            cancelServerBtn.addEventListener('click', () => this.hideAddServerForm());
        }

        // Save server button
        const saveServerBtn = document.getElementById('saveServerBtn');
        if (saveServerBtn) {
            saveServerBtn.addEventListener('click', () => this.saveServer());
        }

        // MCP 클라이언트 상태 새로고침 버튼
        const refreshClientStatusBtn = document.getElementById('refreshClientStatus');
        if (refreshClientStatusBtn) {
            refreshClientStatusBtn.addEventListener('click', () => this.refreshMcpClientStatus());
        }

        // MCP 도구 새로고침 버튼
        const refreshToolsBtn = document.getElementById('refreshTools');
        if (refreshToolsBtn) {
            refreshToolsBtn.addEventListener('click', () => this.refreshMcpTools());
        }

        // MCP 진단 버튼
        const diagnoseMcpBtn = document.getElementById('diagnoseMcp');
        if (diagnoseMcpBtn) {
            diagnoseMcpBtn.addEventListener('click', () => this.diagnoseMcp());
        }
    }

    async loadMCPServers() {
        try {
            console.log('=== Loading MCP servers ===');
            console.log('Current config:', this.config);
            console.log('Config mcpServers:', this.config?.mcpServers);

            // 먼저 Map 초기화
            this.mcpServers = new Map();

            // 설정에서 MCP 서버 정보 로드
            let mcpServersConfig = null;

            // 1. 현재 config에서 확인
            if (this.config?.mcpServers && typeof this.config.mcpServers === 'object') {
                mcpServersConfig = this.config.mcpServers;
                console.log('Found MCP servers in config:', mcpServersConfig);
                console.log('Number of servers in config:', Object.keys(mcpServersConfig).length);
            }

            // 2. 로컬 스토리지에서 확인 (웹 환경 또는 백업)
            if (!mcpServersConfig || Object.keys(mcpServersConfig).length === 0) {
                try {
                    const storedConfig = localStorage.getItem('mcpDesktopConfig');
                    if (storedConfig) {
                        const parsedConfig = JSON.parse(storedConfig);
                        if (parsedConfig?.mcpServers) {
                            mcpServersConfig = parsedConfig.mcpServers;
                            console.log('Found MCP servers in localStorage:', mcpServersConfig);
                            console.log('Number of servers in localStorage:', Object.keys(mcpServersConfig).length);
                        }
                    }
                } catch (storageError) {
                    console.warn('Failed to load from localStorage:', storageError);
                }
            }

            // 3. 서버 정보가 있으면 Map에 추가
            if (mcpServersConfig && typeof mcpServersConfig === 'object') {
                const serverEntries = Object.entries(mcpServersConfig);
                console.log('=== Processing server entries ===');
                console.log('Server entries to load:', serverEntries);

                serverEntries.forEach(([name, config], index) => {
                    if (config && typeof config === 'object') {
                        this.mcpServers.set(name, config);
                        console.log(`${index + 1}. Added server: ${name}`, config);
                    } else {
                        console.warn(`${index + 1}. Skipped invalid server: ${name}`, config);
                    }
                });

                console.log('=== MCP servers loaded successfully ===');
                console.log('Total servers loaded:', this.mcpServers.size);
                console.log('Server names:', Array.from(this.mcpServers.keys()));
            } else {
                console.log('No MCP servers found in config, adding default servers');
                this.addDefaultServers();
            }

            // filer-server 자동 추가 제거 - 사용자가 삭제한 경우 존중
            console.log('Loaded servers from config without auto-adding filer-server');

            console.log('=== Final MCP servers state ===');
            console.log('Final servers count:', this.mcpServers.size);
            console.log('Final server list:', Array.from(this.mcpServers.keys()));

            // 항상 렌더링 수행
            console.log('Calling renderMCPServers...');
            this.renderMCPServers();

            // MCP 클라이언트 상태와 도구 로드
            setTimeout(() => {
                this.refreshMcpClientStatus();
                this.refreshMcpTools();
            }, 1000); // 1초 후 로드

        } catch (error) {
            console.error('Failed to load MCP servers:', error);
            this.mcpServers = new Map();
            this.addDefaultServers();
            this.renderMCPServers();
            this.showNotification('MCP 서버 로드에 실패했습니다.', 'error');
        }
    }

    // 기본 서버들 추가 (Claude Desktop 표준 형식)
    addDefaultServers() {
        console.log('Adding default MCP servers...');

        // 기본 디렉토리는 사용자가 직접 선택하도록 하고, 여기서는 빈 서버만 추가
        // 사용자가 설정에서 허용 디렉토리를 추가해야 함
        console.log('No default servers added - user must configure directories manually');

        // 기본 서버들을 설정에 저장
        this.saveDefaultServersToConfig();
    }

    // 기본 서버들을 설정에 저장
    async saveDefaultServersToConfig() {
        try {
            // MCP 설정을 별도 파일에 저장
            console.log('Saving default servers to mcp_desktop_config.json...');
            await this.saveMcpConfigSafely(this.mcpServers);

            // 메인 설정도 업데이트 (호환성을 위해)
            if (!this.config) {
                this.config = this.getDefaultConfig();
            }
            this.config.mcpServers = Object.fromEntries(this.mcpServers);

            console.log('Default servers saved successfully');
        } catch (error) {
            console.error('Failed to save default servers:', error);
        }
    }

    renderMCPServers() {
        console.log('Rendering MCP servers...');
        console.log('mcpServers Map:', this.mcpServers);
        console.log('mcpServers size:', this.mcpServers?.size || 0);

        const container = document.getElementById('mcpServersList');
        if (!container) {
            console.error('MCP servers list container not found');
            return;
        }

        console.log('Container found:', container);

        // 기존 내용 제거 (빈 상태 메시지만)
        const existingEmptyState = container.querySelector('.empty-state');
        if (existingEmptyState) {
            existingEmptyState.remove();
        }

        // 기존 서버 항목들 제거
        const existingItems = container.querySelectorAll('.mcp-server-item');
        existingItems.forEach(item => item.remove());

        // 서버가 없는 경우
        if (!this.mcpServers || this.mcpServers.size === 0) {
            const emptyState = document.createElement('div');
            emptyState.className = 'empty-state';
            emptyState.textContent = '등록된 MCP 서버가 없습니다.';
            container.appendChild(emptyState);
            console.log('No MCP servers to render - empty state added');
            return;
        }

        console.log('=== Starting to render servers ===');
        console.log('mcpServers keys:', Array.from(this.mcpServers.keys()));

        // 각 서버 렌더링
        let renderedCount = 0;
        this.mcpServers.forEach((config, name) => {
            console.log(`=== Rendering server ${renderedCount + 1}: ${name} ===`);
            console.log('Server config:', config);

            try {
                const serverElement = document.createElement('div');
                serverElement.className = 'mcp-server-item';
                serverElement.dataset.name = name;

                // 서버 정보 표시
                const commandText = config.command || '명령 없음';
                const argsText = config.args && config.args.length > 0 ? ` (${config.args.length}개 인수)` : '';

                serverElement.innerHTML = `
                    <div class="server-info">
                        <div class="server-header">
                            <strong>${name}</strong>
                            <span class="server-status stopped">중지됨</span>
                        </div>
                        <small>${commandText}${argsText}</small>
                    </div>
                    <div class="server-actions">
                        <button class="btn btn-success btn-sm start-server-btn" title="서버 시작">시작</button>
                        <button class="btn btn-danger btn-sm stop-server-btn" disabled title="서버 중지">중지</button>
                        <button class="btn btn-secondary btn-sm edit-server-btn" title="서버 편집">편집</button>
                        <button class="btn btn-danger btn-sm delete-server-btn" title="서버 삭제">삭제</button>
                    </div>
                `;

                // 버튼에 이벤트 리스너 추가
                this.attachServerButtonListeners(serverElement, name);

                container.appendChild(serverElement);
                renderedCount++;
                console.log(`Server ${name} rendered successfully`);

            } catch (error) {
                console.error(`Error rendering server ${name}:`, error);
            }
        });

        console.log(`Successfully rendered ${renderedCount} MCP servers`);

        // 서버 상태 업데이트
        setTimeout(() => {
            this.checkMcpServerStatus();
        }, 100);
    }

    // 서버 버튼 이벤트 리스너 연결을 별도 메서드로 분리
    attachServerButtonListeners(serverElement, name) {
        const startBtn = serverElement.querySelector('.start-server-btn');
        if (startBtn) {
            startBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(`Start button clicked for server: ${name}`);
                this.startServer(name);
            });
        }

        const stopBtn = serverElement.querySelector('.stop-server-btn');
        if (stopBtn) {
            stopBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(`Stop button clicked for server: ${name}`);
                this.stopServer(name);
            });
        }

        const editBtn = serverElement.querySelector('.edit-server-btn');
        if (editBtn) {
            editBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(`Edit button clicked for server: ${name}`);
                this.editServer(name);
            });
        }

        const deleteBtn = serverElement.querySelector('.delete-server-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(`Delete button clicked for server: ${name}`);
                this.removeServer(name);
            });
        }
    }

    async checkMcpServerStatus() {
        try {
            // 부모 창의 electronAPI 확인 (iframe 환경)
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;

            if (parentElectronAPI && parentElectronAPI.getMcpServerStatus) {
                this.serverStatus = await parentElectronAPI.getMcpServerStatus();
                console.log('Current server status:', this.serverStatus);
                this.updateServerStatusUI();
            } else {
                console.log('getMcpServerStatus not available, initializing empty status');
                this.serverStatus = {};
            }
        } catch (error) {
            console.error('Failed to check MCP server status:', error);
            this.serverStatus = {};
        }
    }

    updateServerStatusUI(serverName, isRunning) {
        // 매개변수가 없으면 전체 상태 업데이트
        if (serverName !== undefined && isRunning !== undefined) {
            // 단일 서버 상태 업데이트
            this.serverStatus[serverName] = { running: isRunning };
            console.log(`Updated server status: ${serverName} = ${isRunning}`);
        }

        // 모든 서버 UI 업데이트
        this.mcpServers.forEach((config, name) => {
            const serverElement = document.querySelector(`.mcp-server-item[data-name="${name}"]`);
            if (!serverElement) return;

            const statusElement = serverElement.querySelector('.server-status');
            const startButton = serverElement.querySelector('.start-server-btn');
            const stopButton = serverElement.querySelector('.stop-server-btn');

            const isServerRunning = this.serverStatus[name]?.running || false;

            if (statusElement) {
                statusElement.textContent = isServerRunning ? '실행 중' : '중지됨';
                statusElement.className = `server-status ${isServerRunning ? 'running' : 'stopped'}`;
            }

            if (startButton) startButton.disabled = isServerRunning;
            if (stopButton) stopButton.disabled = !isServerRunning;
        });

        // 서버 상태 컨테이너 업데이트 (항상 실행)
        this.updateServerStatusContainer();
    }

    updateServerStatusContainer() {
        const container = document.getElementById('mcpServerStatus');
        if (!container) {
            console.error('mcpServerStatus container not found');
            return;
        }

        console.log('Updating server status container...');
        console.log('Registered servers:', this.mcpServers);
        console.log('Server status:', this.serverStatus);

        container.innerHTML = '';

        // 등록된 서버가 없는 경우
        if (!this.mcpServers || this.mcpServers.size === 0) {
            container.innerHTML = '<div class="empty-state">등록된 MCP 서버가 없습니다.</div>';
            return;
        }

        // 등록된 모든 서버에 대해 상태 표시
        this.mcpServers.forEach((config, name) => {
            const serverStatus = this.serverStatus[name];
            const isRunning = serverStatus?.running || false;
            const hasError = serverStatus?.error || false;

            console.log(`Server ${name}: running=${isRunning}, error=${hasError}`);

            const statusElement = document.createElement('div');
            statusElement.className = 'server-status-item';

            // 상태에 따른 표시
            let statusClass = 'status-offline';
            let statusText = '중지됨';

            if (hasError) {
                statusClass = 'status-error';
                statusText = '오류';
            } else if (isRunning) {
                statusClass = 'status-online';
                statusText = '실행 중';
            }

            statusElement.innerHTML = `
                <div class="status-indicator ${statusClass}"></div>
                <div class="status-info">
                    <strong>${name}</strong>
                    <small>${statusText}</small>
                    ${config.command ? `<div class="status-command">${config.command}</div>` : ''}
                </div>
            `;
            container.appendChild(statusElement);
        });

        console.log(`Status container updated with ${this.mcpServers.size} servers`);
    }

    async exportSettings() {
        try {
            const dataStr = JSON.stringify(this.config, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'mcp-desktop-settings.json';
            link.click();
            
            this.showNotification('설정이 내보내졌습니다.', 'success');
        } catch (error) {
            console.error('Export failed:', error);
            this.showNotification('설정 내보내기에 실패했습니다.', 'error');
        }
    }

    async importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;

            try {
                const text = await file.text();
                const importedConfig = JSON.parse(text);
                
                // Validate imported config
                if (this.validateConfig(importedConfig)) {
                    this.config = { ...this.getDefaultConfig(), ...importedConfig };
                    this.populateForm();
                    this.mcpServers = new Map(Object.entries(this.config.mcpServers || {}));
                    this.renderMCPServers();
                    this.showNotification('설정이 가져와졌습니다.', 'success');
                } else {
                    throw new Error('Invalid configuration format');
                }
            } catch (error) {
                console.error('Import failed:', error);
                this.showNotification('설정 가져오기에 실패했습니다.', 'error');
            }
        };
        
        input.click();
    }

    validateConfig(config) {
        // Basic validation of config structure
        return config && typeof config === 'object';
    }

    async resetSettings() {
        if (confirm('모든 설정을 기본값으로 재설정하시겠습니까?')) {
            this.config = this.getDefaultConfig();
            this.mcpServers.clear();
            this.populateForm();
            this.renderMCPServers();
            this.showNotification('설정이 재설정되었습니다.', 'success');
        }
    }

    showNotification(message, type = 'info') {
        console.log(`Showing notification: ${message} (${type})`);
        
        // 기존 알림 제거
        const existingNotification = document.querySelector('.notification');
        if (existingNotification) {
            existingNotification.remove();
        }
        
        // 새 알림 생성
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // 알림 추가
        document.body.appendChild(notification);
        
        // 자동 제거 타이머
        setTimeout(() => {
            notification.classList.add('hide');
            setTimeout(() => notification.remove(), 500);
        }, 3000);
        
        return notification;
    }

    // API 타입에 따른 UI 업데이트
    updateApiTypeUI() {
        try {
            const apiTypeSelect = document.getElementById('apiType');
            if (!apiTypeSelect) {
                console.error('API type select element not found');
                return;
            }

            const apiType = apiTypeSelect.value || 'openai';
            console.log('Updating API type UI for:', apiType);

            const apiUrlInput = document.getElementById('apiUrl');
            const apiKeyInput = document.getElementById('apiKey');
            const modelNameInput = document.getElementById('modelName');
            const customApiUrlContainer = document.getElementById('customApiUrlContainer');
            const apiKeyContainer = document.getElementById('apiKeyContainer');

            // 필수 요소들이 존재하는지 확인
            if (!apiUrlInput || !apiKeyInput || !modelNameInput) {
                console.error('Required form elements not found:', {
                    apiUrl: !!apiUrlInput,
                    apiKey: !!apiKeyInput,
                    modelName: !!modelNameInput
                });
                return;
            }

            // API URL과 API 키 그룹 찾기
            const apiUrlGroup = apiUrlInput?.closest('.form-group');
            const apiKeyGroup = apiKeyInput?.closest('.form-group');

            // 모든 그룹 표시 (API 기반 통합)
            if (apiUrlGroup) {
                apiUrlGroup.style.display = 'block';
                console.log('API URL group displayed');
            }
            if (apiKeyGroup) {
                apiKeyGroup.style.display = 'block';
                console.log('API key group displayed');
            }

        // API 타입별 설정
        const configs = {
            openai: {
                apiUrl: 'https://api.openai.com/v1/chat/completions',
                apiUrlPlaceholder: 'https://api.openai.com/v1/chat/completions',
                apiUrlHint: 'OpenAI API 서버 URL입니다.',
                apiKeyRequired: true,
                apiKeyPlaceholder: 'OpenAI API 키를 입력하세요 (sk-...)',
                apiKeyHint: 'OpenAI API 키는 https://platform.openai.com/api-keys 에서 발급받을 수 있습니다.',
                modelPlaceholder: 'gpt-4',
                modelHint: '예: gpt-3.5-turbo, gpt-4, gpt-4-turbo, gpt-4o',
                models: ['gpt-3.5-turbo', 'gpt-3.5-turbo-16k', 'gpt-4', 'gpt-4-turbo', 'gpt-4o', 'gpt-4o-mini'],
                temperature: 0.7,
                maxTokens: 4096
            },
            anthropic: {
                apiUrl: 'https://api.anthropic.com/v1/messages',
                apiUrlPlaceholder: 'https://api.anthropic.com/v1/messages',
                apiUrlHint: 'Anthropic API 서버 URL입니다.',
                apiKeyRequired: true,
                apiKeyPlaceholder: 'Anthropic API 키를 입력하세요 (sk-ant-...)',
                apiKeyHint: 'Anthropic API 키는 https://console.anthropic.com/ 에서 발급받을 수 있습니다.',
                modelPlaceholder: 'claude-3-5-sonnet-20241022',
                modelHint: '예: claude-3-5-sonnet, claude-3-opus, claude-3-haiku',
                models: ['claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
                temperature: 0.7,
                maxTokens: 8192
            },
            ollama: {
                apiUrl: 'http://localhost:11434/v1/chat/completions',
                apiUrlPlaceholder: 'http://localhost:11434/v1/chat/completions',
                apiUrlHint: 'Ollama 서버 URL입니다. 기본값: http://localhost:11434',
                apiKeyRequired: false,
                apiKeyPlaceholder: 'API 키 (Ollama는 일반적으로 불필요)',
                apiKeyHint: 'Ollama는 일반적으로 API 키가 필요하지 않습니다.',
                modelPlaceholder: 'llama3.2',
                modelHint: '예: llama3.2, codellama, mistral, qwen2.5',
                models: ['llama3.2', 'llama3.1', 'llama3', 'codellama', 'mistral', 'qwen2.5', 'phi3', 'gemma2'],
                temperature: 0.7,
                maxTokens: 4096
            },
            llamacpp: {
                apiUrl: 'http://localhost:8080/v1/chat/completions',
                apiUrlPlaceholder: 'http://localhost:8080/v1/chat/completions',
                apiUrlHint: 'LLaMA.cpp 서버 URL입니다. 기본값: http://localhost:8080',
                apiKeyRequired: false,
                apiKeyPlaceholder: 'API 키 (선택사항)',
                apiKeyHint: 'LLaMA.cpp 서버에 API 키가 설정된 경우 입력하세요.',
                modelPlaceholder: 'model',
                modelHint: '서버에서 로드된 모델명을 입력하세요.',
                models: ['model'],
                temperature: 0.7,
                maxTokens: 2048
            },
            custom: {
                apiUrl: '',
                apiUrlPlaceholder: 'https://your-api-server.com/v1/chat/completions',
                apiUrlHint: '사용자 정의 API 서버의 기본 URL을 입력하세요.',
                apiKeyRequired: false,
                apiKeyPlaceholder: 'API 키 (필요한 경우)',
                apiKeyHint: 'API 서버에서 요구하는 인증 키를 입력하세요.',
                modelPlaceholder: 'model-name',
                modelHint: 'API에서 지원하는 모델명을 입력하세요.',
                models: [],
                temperature: 0.7,
                maxTokens: 2048
            },
            // 하위 호환성을 위한 별칭
            local: {
                apiUrl: 'http://localhost:11434',
                apiUrlPlaceholder: 'http://localhost:11434',
                apiUrlHint: 'Ollama 서버 URL입니다. (구버전 호환)',
                apiKeyRequired: false,
                apiKeyPlaceholder: 'API 키 (Ollama는 일반적으로 불필요)',
                apiKeyHint: 'Ollama는 일반적으로 API 키가 필요하지 않습니다.',
                modelPlaceholder: 'llama4-maverick',
                modelHint: '예: llama2, codellama, mistral, llama4-maverick',
                models: []
            },
            llama: {
                apiUrl: 'http://localhost:8080',
                apiUrlPlaceholder: 'http://localhost:8080',
                apiUrlHint: 'LLaMA.cpp 서버 URL입니다. (구버전 호환)',
                apiKeyRequired: false,
                apiKeyPlaceholder: 'API 키 (선택사항)',
                apiKeyHint: 'LLaMA.cpp 서버에 API 키가 설정된 경우 입력하세요.',
                modelPlaceholder: 'model',
                modelHint: '서버에서 로드된 모델명을 입력하세요.',
                models: []
            }
        };

        const config = configs[apiType] || configs.openai;

        // API 타입 설명 업데이트
        const apiTypeDescription = document.getElementById('apiTypeDescription');
        if (apiTypeDescription) {
            const descriptions = {
                openai: 'OpenAI API (GPT 모델)',
                anthropic: 'Anthropic API (Claude 모델)',
                ollama: 'Ollama 로컬 서버 (다중 엔드포인트 지원)',
                llamacpp: 'LLaMA.cpp 서버 (OpenAI 호환)',
                custom: '사용자 정의 API 서버',
                local: 'Ollama 로컬 서버 (구버전 호환)',
                llama: 'LLaMA.cpp 서버 (구버전 호환)'
            };
            apiTypeDescription.textContent = descriptions[apiType] || descriptions.openai;
        }

        // UI 업데이트 - 자동으로 기본값 설정 (강제 업데이트)
        if (apiUrlInput) {
            // API 타입 변경 시 항상 기본 URL로 설정
            apiUrlInput.value = config.apiUrl;
            apiUrlInput.placeholder = config.apiUrlPlaceholder;
            console.log(`API URL forcefully updated to: ${config.apiUrl} for API type: ${apiType}`);

            // 시각적 피드백
            apiUrlInput.style.transition = 'all 0.3s ease';
            apiUrlInput.style.backgroundColor = 'var(--accent-color-light)';
            setTimeout(() => {
                apiUrlInput.style.backgroundColor = '';
            }, 1000);
        } else {
            console.warn('apiUrlInput element not found');
        }

        if (apiKeyInput) {
            apiKeyInput.required = config.apiKeyRequired;
            apiKeyInput.placeholder = config.apiKeyPlaceholder;

            // API 키 필드 표시/숨김 및 스타일 업데이트
            const apiKeyGroup = apiKeyInput.closest('.form-group');
            if (!config.apiKeyRequired) {
                if (apiKeyGroup) {
                    apiKeyGroup.style.opacity = '0.7';
                    apiKeyGroup.style.pointerEvents = 'none';
                }
                apiKeyInput.value = ''; // API 키가 불필요한 경우 값 제거
            } else {
                if (apiKeyGroup) {
                    apiKeyGroup.style.opacity = '1';
                    apiKeyGroup.style.pointerEvents = 'auto';
                }
            }

            console.log(`API key field updated - required: ${config.apiKeyRequired}`);
        }

        if (modelNameInput) {
            modelNameInput.value = config.modelPlaceholder;
            modelNameInput.placeholder = config.modelPlaceholder;
            console.log(`Model name forcefully updated to: ${config.modelPlaceholder} for API type: ${apiType}`);

            // 시각적 피드백
            modelNameInput.style.transition = 'all 0.3s ease';
            modelNameInput.style.backgroundColor = 'var(--accent-color-light)';
            setTimeout(() => {
                modelNameInput.style.backgroundColor = '';
            }, 1000);
        } else {
            console.warn('modelNameInput element not found');
        }

        // Temperature와 Max Tokens 자동 설정 (실시간 업데이트)
        const temperatureInput = document.getElementById('temperature');
        const temperatureValue = document.getElementById('temperatureValue');
        const maxTokensInput = document.getElementById('maxTokens');

        if (temperatureInput && config.temperature !== undefined) {
            temperatureInput.value = config.temperature;
            if (temperatureValue) {
                temperatureValue.textContent = config.temperature;
            }
            console.log(`Temperature updated to: ${config.temperature} for API type: ${apiType}`);

            // 시각적 피드백 추가
            temperatureInput.style.transition = 'all 0.3s ease';
            temperatureInput.style.backgroundColor = 'var(--accent-color-light)';
            setTimeout(() => {
                temperatureInput.style.backgroundColor = '';
            }, 1000);
        }

        if (maxTokensInput && config.maxTokens !== undefined) {
            maxTokensInput.value = config.maxTokens;
            console.log(`Max tokens updated to: ${config.maxTokens} for API type: ${apiType}`);

            // 시각적 피드백 추가
            maxTokensInput.style.transition = 'all 0.3s ease';
            maxTokensInput.style.backgroundColor = 'var(--accent-color-light)';
            setTimeout(() => {
                maxTokensInput.style.backgroundColor = '';
            }, 1000);
        }

        // 모델 선택 드롭다운 업데이트 (동적으로 옵션 추가)
        this.updateModelSelectDropdown(config.models, config.modelPlaceholder);

        // 힌트 텍스트 업데이트
        const apiUrlHint = document.querySelector('.api-url-hint');
        const apiKeyHint = document.querySelector('.api-key-hint');
        const modelHint = document.querySelector('.model-hint');

        if (apiUrlHint) apiUrlHint.textContent = config.apiUrlHint;
        if (apiKeyHint) apiKeyHint.textContent = config.apiKeyHint;
        if (modelHint) modelHint.textContent = config.modelHint;

        // 사용자 정의 API URL 컨테이너 표시/숨김
        if (customApiUrlContainer) {
            customApiUrlContainer.style.display = apiType === 'custom' ? 'block' : 'none';
        }

        // API 키 필수 여부에 따른 스타일 조정
        if (apiKeyContainer) {
            if (!config.apiKeyRequired) {
                apiKeyContainer.style.opacity = '0.7';
                const label = apiKeyContainer.querySelector('label');
                if (label && !label.textContent.includes('(선택사항)')) {
                    label.textContent += ' (선택사항)';
                }
            } else {
                apiKeyContainer.style.opacity = '1';
                const label = apiKeyContainer.querySelector('label');
                if (label) {
                    label.textContent = label.textContent.replace(' (선택사항)', '');
                }
            }
        }

        console.log(`UI updated for ${apiType}:`, config);

        // 설정 업데이트 및 저장
        this.updateConfig();
        this.onLlmConfigChanged();

        } catch (error) {
            console.error('Error updating API type UI:', error);
        }
    }

    // 모델 선택 드롭다운 업데이트 (동적으로 옵션 추가)
    updateModelSelectDropdown(models, defaultModel) {
        const modelSelect = document.getElementById('modelSelect');
        if (!modelSelect) {
            console.warn('modelSelect element not found');
            return;
        }

        console.log('Updating model select dropdown with models:', models);
        console.log('Default model:', defaultModel);

        // 기존 옵션 모두 제거
        modelSelect.innerHTML = '';

        // 기본 옵션 추가
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '적용 모델을 선택하세요';
        modelSelect.appendChild(defaultOption);

        // 새 모델 옵션들을 동적으로 추가
        if (models && models.length > 0) {
            models.forEach((model, index) => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                modelSelect.appendChild(option);

                console.log(`Added model option ${index + 1}: ${model}`);
            });

            // 기본 모델 선택 및 모델명 입력 필드 업데이트
            if (defaultModel && models.includes(defaultModel)) {
                modelSelect.value = defaultModel;

                // 모델명 입력 필드도 자동 업데이트
                const modelNameInput = document.getElementById('modelName');
                if (modelNameInput) {
                    modelNameInput.value = defaultModel;
                    console.log(`Model name input updated to: ${defaultModel}`);

                    // 시각적 피드백
                    modelNameInput.style.transition = 'all 0.3s ease';
                    modelNameInput.style.backgroundColor = 'var(--accent-color-light)';
                    setTimeout(() => {
                        modelNameInput.style.backgroundColor = '';
                    }, 1000);
                }
            }

            // 드롭다운에 시각적 피드백
            modelSelect.style.transition = 'all 0.3s ease';
            modelSelect.style.borderColor = 'var(--accent-color)';
            setTimeout(() => {
                modelSelect.style.borderColor = '';
            }, 1000);
        }

        console.log(`Model select dropdown updated with ${models?.length || 0} models`);
    }

    // LLM 모델 변경 시 호출되는 메서드 (즉시 UI 업데이트)
    onLlmModelChanged(llmModel) {
        console.log('LLM model changed to:', llmModel);

        try {
            // API 타입 필드 업데이트 (숨겨진 필드)
            const apiTypeSelect = document.getElementById('apiType');
            if (apiTypeSelect) {
                apiTypeSelect.value = llmModel;
                console.log('API type updated to:', llmModel);
            }

            // 즉시 해당 LLM 모델의 설정으로 UI 업데이트
            console.log('Immediately updating UI for LLM model:', llmModel);
            this.updateApiTypeUI();

            // 사용자에게 변경 알림
            this.showNotification(`${this.getLlmModelDisplayName(llmModel)} 모델로 설정이 변경되었습니다.`, 'success');

            // 설정 저장
            this.updateConfig();
            this.onLlmConfigChanged();

            console.log('LLM model change completed successfully');
        } catch (error) {
            console.error('Error in onLlmModelChanged:', error);
            this.showNotification('LLM 모델 변경 중 오류가 발생했습니다.', 'error');
        }
    }

    // LLM 모델 표시명 반환
    getLlmModelDisplayName(llmModel) {
        const displayNames = {
            'anthropic': 'Anthropic (Claude)',
            'openai': 'OpenAI (GPT)',
            'ollama': 'Ollama (로컬)',
            'llamacpp': 'LLaMA.cpp',
            'custom': '사용자 정의'
        };
        return displayNames[llmModel] || llmModel;
    }

    // LLM 모델 선택 초기화
    initializeLlmModelSelect() {
        console.log('Initializing LLM model select...');

        const llmModelSelect = document.getElementById('llmModelSelect');
        if (!llmModelSelect) {
            console.warn('llmModelSelect element not found');
            return;
        }

        // 현재 설정된 API 타입 확인
        const currentApiType = this.config?.llmConfig?.apiType || 'anthropic';
        console.log('Current API type:', currentApiType);

        // LLM 모델 선택 드롭다운 값 설정
        llmModelSelect.value = currentApiType;

        // 이벤트 리스너가 제대로 연결되었는지 확인
        const hasListener = llmModelSelect.getAttribute('data-listener-attached');
        if (!hasListener) {
            console.log('Attaching LLM model select listener...');
            llmModelSelect.addEventListener('change', (e) => {
                const selectedLlmModel = e.target.value;
                console.log('LLM model changed via event listener to:', selectedLlmModel);
                this.onLlmModelChanged(selectedLlmModel);
            });
            llmModelSelect.setAttribute('data-listener-attached', 'true');
        }

        console.log('LLM model select initialized with value:', currentApiType);
    }

    // API 키 표시/숨기기 토글
    toggleApiKeyVisibility() {
        const apiKeyInput = document.getElementById('apiKey');
        const toggleBtn = document.getElementById('toggleApiKey');
        const icon = toggleBtn?.querySelector('i');

        if (apiKeyInput && toggleBtn) {
            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                if (icon) icon.className = 'fas fa-eye-slash';
            } else {
                apiKeyInput.type = 'password';
                if (icon) icon.className = 'fas fa-eye';
            }
        }
    }

    // 모델 목록 새로고침
    async refreshModelsList() {
        const refreshBtn = document.getElementById('refreshModels');
        const originalText = refreshBtn?.textContent;

        if (refreshBtn) {
            refreshBtn.textContent = '새로고침 중...';
            refreshBtn.disabled = true;
        }

        try {
            // API 타입에 따라 모델 목록 가져오기
            const apiType = document.getElementById('apiType')?.value || 'openai';

            if (apiType === 'ollama' || apiType === 'local') {
                const apiUrl = document.getElementById('apiUrl')?.value || 'http://localhost:11434';
                const response = await fetch(`${apiUrl}/api/tags`);

                if (response.ok) {
                    const data = await response.json();

                    // 모델 선택 드롭다운 업데이트
                    const modelSelect = document.getElementById('modelName');
                    if (modelSelect && data.models && data.models.length > 0) {
                        const currentValue = modelSelect.value;

                        // 기존 옵션 제거 (첫 번째 옵션 제외)
                        while (modelSelect.children.length > 1) {
                            modelSelect.removeChild(modelSelect.lastChild);
                        }

                        // 새 모델 옵션 추가
                        data.models.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.name;
                            option.textContent = model.name;
                            modelSelect.appendChild(option);
                        });

                        // 이전 값이 있으면 복원, 없으면 첫 번째 모델 선택
                        if (currentValue && data.models.some(m => m.name === currentValue)) {
                            modelSelect.value = currentValue;
                        } else if (data.models.length > 0) {
                            modelSelect.value = data.models[0].name;
                        }

                        // 설정 업데이트 및 메인 앱에 알림
                        this.updateConfig();
                        this.onLlmConfigChanged();
                        this.notifyModelDisplayUpdate();
                    }

                    this.showNotification(`${data.models?.length || 0}개의 모델을 찾았습니다.`, 'success');
                } else {
                    throw new Error('모델 목록을 가져올 수 없습니다.');
                }
            } else if (apiType === 'llamacpp' || apiType === 'llama') {
                // LLaMA 서버 모델 목록 시도
                const apiUrl = document.getElementById('apiUrl')?.value || 'http://localhost:8080';
                try {
                    const response = await fetch(`${apiUrl}/v1/models`);
                    if (response.ok) {
                        const data = await response.json();

                        if (data.data && data.data.length > 0) {
                            // 모델 선택 드롭다운 업데이트
                            const modelSelect = document.getElementById('modelName');
                            if (modelSelect) {
                                const currentValue = modelSelect.value;

                                // 기존 옵션 제거
                                while (modelSelect.children.length > 1) {
                                    modelSelect.removeChild(modelSelect.lastChild);
                                }

                                // 새 모델 옵션 추가
                                data.data.forEach(model => {
                                    const option = document.createElement('option');
                                    option.value = model.id;
                                    option.textContent = model.id;
                                    modelSelect.appendChild(option);
                                });

                                // 이전 값 복원 또는 첫 번째 모델 선택
                                if (currentValue && data.data.some(m => m.id === currentValue)) {
                                    modelSelect.value = currentValue;
                                } else if (data.data.length > 0) {
                                    modelSelect.value = data.data[0].id;
                                }

                                // 설정 업데이트 및 메인 앱에 알림
                                this.updateConfig();
                                this.onLlmConfigChanged();
                                this.notifyModelDisplayUpdate();
                            }

                            this.showNotification(`${data.data?.length || 0}개의 모델을 찾았습니다.`, 'success');
                        } else {
                            throw new Error('LLaMA 서버에서 모델 목록을 가져올 수 없습니다.');
                        }
                    } else {
                        throw new Error('LLaMA 서버 응답 오류');
                    }
                } catch (llamaError) {
                    console.error('LLaMA server model fetch failed:', llamaError);
                    this.showNotification('LLaMA 서버 모델 목록 새로고침에 실패했습니다.', 'error');
                }
            } else {
                this.showNotification('이 API 타입은 모델 목록 새로고침을 지원하지 않습니다.', 'info');
            }
        } catch (error) {
            console.error('Failed to refresh models:', error);
            this.showNotification('모델 목록 새로고침에 실패했습니다: ' + error.message, 'error');
        } finally {
            if (refreshBtn) {
                refreshBtn.textContent = originalText;
                refreshBtn.disabled = false;
            }
        }
    }

    // 메인 앱의 모델명 표시 업데이트 알림
    notifyModelDisplayUpdate() {
        try {
            // 현재 설정 수집
            const currentConfig = {
                apiType: document.getElementById('apiType')?.value,
                model: document.getElementById('modelName')?.value,
                apiUrl: document.getElementById('apiUrl')?.value,
                temperature: parseFloat(document.getElementById('temperature')?.value || 0.7),
                maxTokens: parseInt(document.getElementById('maxTokens')?.value || 2048)
            };

            console.log('Notifying model display update:', currentConfig);

            // 메인 창에 메시지 전송 (설정 창이 별도 창인 경우)
            if (window.opener && window.opener.app) {
                window.opener.app.updateModelDisplay(currentConfig);
                console.log('Model display update sent to main window');
            }

            // 부모 창에 메시지 전송 (모달인 경우)
            if (window.parent && window.parent !== window && window.parent.app) {
                window.parent.app.updateModelDisplay(currentConfig);
                console.log('Model display update sent to parent window');
            }

            // 메인 프로세스를 통한 알림
            if (window.electronAPI && window.electronAPI.notifyModelDisplayUpdate) {
                window.electronAPI.notifyModelDisplayUpdate(currentConfig);
                console.log('Model display update sent via IPC');
            }

        } catch (error) {
            console.error('Error notifying model display update:', error);
        }
    }

    // 언어 적용 메서드 개선
    applyLanguage(language) {
        console.log(`Applying language: ${language}`);
        document.documentElement.setAttribute('data-language', language);
        
        // 언어별 텍스트 변경 로직
        const translations = {
            'ko': {
                'general': '일반 설정',
                'language': '언어',
                'startAtLogin': '시스템 시작 시 자동 실행',
                'minimizeToTray': '시스템 트레이로 최소화',
                'llm': 'LLM 설정',
                'apiType': 'API 유형',
                'apiUrl': 'API URL',
                'apiKey': 'API 키',
                'customApiUrl': '사용자 정의 API URL',
                'modelName': '모델 이름',
                'temperature': '온도',
                'maxTokens': '최대 토큰 수',
                'appearance': '외관',
                'theme': '테마',
                'fontSize': '글꼴 크기',
                'fontFamily': '글꼴',
                'advanced': '고급 설정',
                'enableDevTools': '개발자 도구 활성화',
                'enableLogging': '로깅 활성화',
                'save': '저장',
                'cancel': '취소',
                'testConnection': '연결 테스트',
                'dark': '다크 모드',
                'light': '라이트 모드',
                'system': '시스템 설정 따라가기',
                'mcp': 'MCP 서버 관리',
                'mcpServers': '등록된 서버',
                'addServer': '서버 추가',
                'serverName': '서버 이름',
                'serverCommand': '실행 명령',
                'serverArgs': '인수',
                'serverEnv': '환경 변수 (JSON)',
                'saveServer': '저장',
                'cancelServer': '취소',
                'mcpServerStatus': 'MCP 서버 상태',
                'startServer': '시작',
                'stopServer': '중지',
                'deleteServer': '삭제',
                'editServer': '편집',
                'refreshModels': '모델 목록 새로고침',
                'apiKeyHint': 'API 키는 암호화되어 저장됩니다.',
                'localOllama': '로컬 (Ollama)',
                'openai': 'OpenAI',
                'anthropic': 'Anthropic',
                'custom': '사용자 정의',
                'connectionTest': '연결 테스트',
                'exportSettings': '설정 내보내기',
                'importSettings': '설정 가져오기',
                'resetSettings': '설정 초기화',
                'dataManagement': '데이터 관리'
            },
            'en': {
                'general': 'General Settings',
                'language': 'Language',
                'startAtLogin': 'Start at login',
                'minimizeToTray': 'Minimize to tray',
                'llm': 'LLM Settings',
                'apiType': 'API Type',
                'apiUrl': 'API URL',
                'apiKey': 'API Key',
                'customApiUrl': 'Custom API URL',
                'modelName': 'Model Name',
                'temperature': 'Temperature',
                'maxTokens': 'Max Tokens',
                'appearance': 'Appearance',
                'theme': 'Theme',
                'fontSize': 'Font Size',
                'fontFamily': 'Font Family',
                'advanced': 'Advanced Settings',
                'enableDevTools': 'Enable Developer Tools',
                'enableLogging': 'Enable Logging',
                'save': 'Save',
                'cancel': 'Cancel',
                'testConnection': 'Test Connection',
                'dark': 'Dark Mode',
                'light': 'Light Mode',
                'system': 'System Default',
                'mcp': 'MCP Server Management',
                'mcpServers': 'Registered Servers',
                'addServer': 'Add Server',
                'serverName': 'Server Name',
                'serverCommand': 'Command',
                'serverArgs': 'Arguments',
                'serverEnv': 'Environment Variables (JSON)',
                'saveServer': 'Save',
                'cancelServer': 'Cancel',
                'mcpServerStatus': 'MCP Server Status',
                'startServer': 'Start',
                'stopServer': 'Stop',
                'deleteServer': 'Delete',
                'editServer': 'Edit',
                'refreshModels': 'Refresh Models',
                'apiKeyHint': 'API key is stored encrypted.',
                'localOllama': 'Local (Ollama)',
                'openai': 'OpenAI',
                'anthropic': 'Anthropic',
                'custom': 'Custom',
                'connectionTest': 'Connection Test',
                'exportSettings': 'Export Settings',
                'importSettings': 'Import Settings',
                'resetSettings': 'Reset Settings',
                'dataManagement': 'Data Management'
            }
        };
        
        // 현재 언어의 번역 가져오기
        const currentTranslations = translations[language] || translations['en'];
        
        // 모든 번역 가능한 요소에 적용
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            if (currentTranslations[key]) {
                element.textContent = currentTranslations[key];
            }
        });
        
        // 특정 요소 직접 번역
        const elementsToTranslate = {
            '#general h2': 'general',
            '#llm h2': 'llm',
            '#appearance h2': 'appearance',
            '#advanced h2': 'advanced',
            '#mcp h2': 'mcp',
            'label[for="language"]': 'language',
            'label[for="apiType"]': 'apiType',
            'label[for="apiUrl"]': 'apiUrl',
            'label[for="apiKey"]': 'apiKey',
            'label[for="customApiUrl"]': 'customApiUrl',
            'label[for="modelName"]': 'modelName',
            'label[for="temperature"]': 'temperature',
            'label[for="maxTokens"]': 'maxTokens',
            'label[for="theme"]': 'theme',
            'label[for="fontSize"]': 'fontSize',
            'label[for="fontFamily"]': 'fontFamily',
            '#startAtLogin + span': 'startAtLogin',
            '#minimizeToTray + span': 'minimizeToTray',
            '#enableDevTools + span': 'enableDevTools',
            '#enableLogging + span': 'enableLogging',
            '#saveBtn': 'save',
            '#cancelBtn': 'cancel',
            '#testConnection': 'testConnection',
            '#refreshModels': 'refreshModels',
            '#addServerBtn': 'addServer',
            '#saveServerBtn': 'saveServer',
            '#cancelServerBtn': 'cancelServer',
            '.api-key-group small': 'apiKeyHint',
            '#exportSettings': 'exportSettings',
            '#importSettings': 'importSettings',
            '#resetSettings': 'resetSettings',
            '.settings-group h3:contains("데이터 관리")': 'dataManagement',
            '.settings-group h3:contains("Data Management")': 'dataManagement'
        };
        
        Object.entries(elementsToTranslate).forEach(([selector, key]) => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (currentTranslations[key]) {
                        element.textContent = currentTranslations[key];
                    }
                });
            } catch (error) {
                console.error(`Error translating selector ${selector}:`, error);
            }
        });
        
        // 셀렉트 박스 옵션 번역
        try {
            // API 유형 옵션 번역
            const apiTypeSelect = document.querySelector('#apiType');
            if (apiTypeSelect) {
                const apiTypeOptions = apiTypeSelect.options;
                if (apiTypeOptions[0] && currentTranslations['localOllama']) apiTypeOptions[0].textContent = currentTranslations['localOllama'];
                if (apiTypeOptions[1] && currentTranslations['openai']) apiTypeOptions[1].textContent = currentTranslations['openai'];
                if (apiTypeOptions[2] && currentTranslations['anthropic']) apiTypeOptions[2].textContent = currentTranslations['anthropic'];
                if (apiTypeOptions[3] && currentTranslations['custom']) apiTypeOptions[3].textContent = currentTranslations['custom'];
            }
            
            // 테마 옵션 번역
            const themeSelect = document.querySelector('#theme');
            if (themeSelect) {
                const themeOptions = themeSelect.options;
                if (themeOptions[0] && currentTranslations['dark']) themeOptions[0].textContent = currentTranslations['dark'];
                if (themeOptions[1] && currentTranslations['light']) themeOptions[1].textContent = currentTranslations['light'];
                if (themeOptions[2] && currentTranslations['system']) themeOptions[2].textContent = currentTranslations['system'];
            }
        } catch (error) {
            console.error('Error translating select options:', error);
        }
        
        console.log(`Language changed to: ${language}`);
    }

    // 허용 디렉토리 관련 메서드 추가
    setupServerFormListeners() {
        console.log('Setting up server form listeners...');

        // 지연된 초기화를 위해 setTimeout 사용
        setTimeout(() => {
            this.initializeServerFormElements();
        }, 100);
    }

    initializeServerFormElements() {
        console.log('Initializing server form elements...');

        // 서버 추가/취소 버튼
        const addServerBtn = document.getElementById('addServerBtn');
        if (addServerBtn) {
            console.log('Found addServerBtn, adding click listener');
            // 기존 리스너 제거 후 새로 추가
            addServerBtn.removeEventListener('click', this.showAddServerForm);
            addServerBtn.addEventListener('click', () => this.showAddServerForm());
        } else {
            console.warn('addServerBtn not found in DOM');
        }

        const saveServerBtn = document.getElementById('saveServerBtn');
        if (saveServerBtn) {
            console.log('Found saveServerBtn, adding click listener');
            saveServerBtn.removeEventListener('click', this.saveServer);
            saveServerBtn.addEventListener('click', () => this.saveServer());
        } else {
            console.warn('saveServerBtn not found in DOM');
        }

        const cancelServerBtn = document.getElementById('cancelServerBtn');
        if (cancelServerBtn) {
            console.log('Found cancelServerBtn, adding click listener');
            cancelServerBtn.removeEventListener('click', this.hideAddServerForm);
            cancelServerBtn.addEventListener('click', () => this.hideAddServerForm());
        } else {
            console.warn('cancelServerBtn not found in DOM');
        }

        // 디렉토리 관련 버튼 - 더 견고한 방식으로 처리
        this.setupDirectoryControls();
    }

    setupDirectoryControls() {
        console.log('Setting up directory controls...');

        // 디렉토리 추가 버튼
        const addDirectoryBtn = document.getElementById('addDirectoryBtn');
        if (addDirectoryBtn) {
            console.log('Found addDirectoryBtn, setting up click listener');
            // 기존 리스너 제거
            addDirectoryBtn.removeEventListener('click', this.handleAddDirectory);
            // 새 리스너 추가
            this.handleAddDirectory = () => {
                console.log('Directory add button clicked');
                this.addAllowedDirectory();
            };
            addDirectoryBtn.addEventListener('click', this.handleAddDirectory);
        } else {
            console.warn('addDirectoryBtn not found in DOM');
        }

        // 디렉토리 브라우저 버튼
        const browseDirectoryBtn = document.getElementById('browseDirectoryBtn');
        if (browseDirectoryBtn) {
            console.log('Found browseDirectoryBtn, setting up click listener');
            browseDirectoryBtn.removeEventListener('click', this.handleBrowseDirectory);
            this.handleBrowseDirectory = () => {
                console.log('Directory browse button clicked');
                this.browseDirectory();
            };
            browseDirectoryBtn.addEventListener('click', this.handleBrowseDirectory);
        } else {
            console.warn('browseDirectoryBtn not found in DOM');
        }

        // 디렉토리 입력 필드
        const directoryInput = document.getElementById('directoryInput');
        if (directoryInput) {
            console.log('Found directoryInput, setting up keypress listener');
            directoryInput.removeEventListener('keypress', this.handleDirectoryKeypress);
            this.handleDirectoryKeypress = (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('Enter key pressed in directory input');
                    this.addAllowedDirectory();
                }
            };
            directoryInput.addEventListener('keypress', this.handleDirectoryKeypress);
        } else {
            console.warn('directoryInput not found in DOM');
        }

        // 초기 렌더링
        this.renderAllowedDirectories();
    }

    // 허용 디렉토리 추가 메서드
    async addAllowedDirectory() {
        console.log('addAllowedDirectory called');

        try {
            const directoryInput = document.getElementById('directoryInput');
            if (!directoryInput) {
                console.error('directoryInput element not found');
                this.showNotification('디렉토리 입력 필드를 찾을 수 없습니다.', 'error');
                return;
            }

            const directory = directoryInput.value.trim();
            console.log('Directory to add:', directory);

            if (!directory) {
                console.log('Empty directory path');
                this.showNotification('디렉토리 경로를 입력하세요.', 'error');
                directoryInput.focus();
                return;
            }

            // 경로 유효성 검사 (기본적인 검사)
            if (directory.length < 2) {
                console.log('Directory path too short');
                this.showNotification('유효한 디렉토리 경로를 입력하세요.', 'error');
                return;
            }

            // 중복 체크
            if (this.allowedDirectories.includes(directory)) {
                console.log('Directory already exists:', directory);
                this.showNotification('이미 추가된 디렉토리입니다.', 'error');
                directoryInput.value = '';
                directoryInput.focus();
                return;
            }

            // 디렉토리 추가
            this.allowedDirectories.push(directory);
            directoryInput.value = '';

            console.log('Directory added successfully. Updated allowedDirectories:', this.allowedDirectories);

            // UI 업데이트
            this.renderAllowedDirectories();

            this.showNotification(`디렉토리가 추가되었습니다: ${directory}`, 'success');

            // 입력 필드에 포커스 유지
            setTimeout(() => {
                directoryInput.focus();
            }, 100);

        } catch (error) {
            console.error('Error in addAllowedDirectory:', error);
            this.showNotification('디렉토리 추가 중 오류가 발생했습니다: ' + error.message, 'error');
        }
    }

    // 디렉토리 브라우저 열기 메서드 개선
    async browseDirectory() {
        try {
            console.log('Opening directory browser...');

            // 부모 창의 electronAPI 확인 (iframe 환경)
            const parentElectronAPI = window.parent?.electronAPI || window.electronAPI;

            if (parentElectronAPI && parentElectronAPI.showOpenDialog) {
                // 대화상자 옵션
                const options = {
                    properties: ['openDirectory'],
                    title: '허용 디렉토리 선택',
                    buttonLabel: '선택'
                };

                console.log('Calling showOpenDialog with options:', options);

                // 대화상자 열기
                const result = await parentElectronAPI.showOpenDialog(options);
                console.log('Directory browser result:', result);

                // 결과 처리
                if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
                    const selectedPath = result.filePaths[0];
                    console.log('Selected directory:', selectedPath);

                    // 입력 필드 찾기
                    const directoryInput = document.getElementById('directoryInput');
                    if (directoryInput) {
                        directoryInput.value = selectedPath;
                        console.log('Set directory input value to:', selectedPath);
                        // 자동으로 디렉토리 추가
                        this.addAllowedDirectory();
                    } else {
                        console.error('directoryInput element not found');
                        this.showNotification('디렉토리 입력 필드를 찾을 수 없습니다.', 'error');
                    }
                } else {
                    console.log('Directory selection canceled or no directory selected');
                }
            } else {
                // 웹 환경에서는 일반적인 디렉토리 경로 예시 제공
                console.log('Running in web environment, providing directory examples');
                this.showDirectoryInputHelper();
            }
        } catch (error) {
            console.error('Failed to open directory browser:', error);
            this.showDirectoryInputHelper();
        }
    }

    // 웹 환경에서 디렉토리 입력 도우미
    showDirectoryInputHelper() {
        const directoryInput = document.getElementById('directoryInput');
        if (directoryInput) {
            // 운영체제별 예시 경로 제공
            const isWindows = navigator.platform.toLowerCase().includes('win');
            const examplePath = isWindows ? 'C:\\Users\\<USER>\\Documents' : '/home/<USER>/documents';

            // 플레이스홀더 업데이트
            directoryInput.placeholder = `예: ${examplePath}`;
            directoryInput.focus();

            // 도움말 메시지 표시
            this.showNotification(`디렉토리 경로를 직접 입력하세요. 예: ${examplePath}`, 'info');

            // 일반적인 디렉토리 경로 버튼 추가
            this.showCommonDirectories();
        }
    }

    // 일반적인 디렉토리 경로 버튼 표시
    showCommonDirectories() {
        const container = document.getElementById('allowedDirectoriesList');
        if (!container) return;

        // 기존 도움말 제거
        const existingHelper = container.querySelector('.directory-helper');
        if (existingHelper) {
            existingHelper.remove();
        }

        // 일반적인 경로들
        const isWindows = navigator.platform.toLowerCase().includes('win');
        const commonPaths = isWindows ? [
            'C:\\Users\\<USER>\\Documents',
            'C:\\Users\\<USER>\\Desktop',
            'C:\\Users\\<USER>\\Downloads',
            'C:\\temp',
            'C:\\workspace'
        ] : [
            '/home/' + (process.env.USER || 'username') + '/Documents',
            '/home/' + (process.env.USER || 'username') + '/Desktop',
            '/home/' + (process.env.USER || 'username') + '/Downloads',
            '/tmp',
            '/workspace'
        ];

        const helper = document.createElement('div');
        helper.className = 'directory-helper';
        helper.innerHTML = `
            <div style="margin: 10px 0; padding: 10px; background: var(--bg-secondary); border-radius: 6px;">
                <h5 style="margin: 0 0 10px 0; color: var(--text-primary);">일반적인 디렉토리 경로:</h5>
                <div class="common-paths">
                    ${commonPaths.map(path => `
                        <button class="btn btn-sm btn-secondary common-path-btn" data-path="${path}" style="margin: 2px; font-size: 11px;">
                            ${path}
                        </button>
                    `).join('')}
                </div>
                <small style="color: var(--text-secondary); margin-top: 10px; display: block;">
                    위 경로를 클릭하거나 직접 입력하세요. 경로는 실제 존재하는 디렉토리여야 합니다.
                </small>
            </div>
        `;

        container.appendChild(helper);

        // 일반 경로 버튼 이벤트 리스너
        helper.querySelectorAll('.common-path-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const path = e.target.getAttribute('data-path');
                const directoryInput = document.getElementById('directoryInput');
                if (directoryInput) {
                    directoryInput.value = path;
                    directoryInput.focus();
                }
            });
        });
    }

    // 허용 디렉토리 렌더링
    renderAllowedDirectories() {
        const container = document.getElementById('allowedDirectoriesList');
        if (!container) {
            console.error('allowedDirectoriesList container not found');
            return;
        }

        // 기존 디렉토리 항목만 제거 (도움말은 유지)
        const directoryItems = container.querySelectorAll('.directory-item');
        directoryItems.forEach(item => item.remove());

        // 빈 상태 메시지 제거
        const emptyState = container.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }

        if (this.allowedDirectories.length === 0) {
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'empty-state';
            emptyDiv.textContent = '허용된 디렉토리가 없습니다.';
            container.appendChild(emptyDiv);
            return;
        }

        this.allowedDirectories.forEach((directory, index) => {
            const item = document.createElement('div');
            item.className = 'directory-item';

            const isDefault = index === 0;
            const defaultBadge = isDefault ? '<span class="default-badge">기본</span>' : '';

            item.innerHTML = `
                <div class="directory-path">${directory} ${defaultBadge}</div>
                <div class="directory-actions">
                    ${!isDefault ? `<button class="btn btn-sm btn-secondary make-default-btn" data-index="${index}" title="기본 디렉토리로 설정">
                        <i class="fas fa-star"></i>
                    </button>` : ''}
                    <button class="btn btn-sm btn-danger remove-directory-btn" data-index="${index}" title="디렉토리 제거">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            container.appendChild(item);
        });

        // 이벤트 리스너 추가
        container.querySelectorAll('.remove-directory-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                this.removeAllowedDirectory(index);
            });
        });

        container.querySelectorAll('.make-default-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                this.makeDefaultDirectory(index);
            });
        });
    }

    // 허용 디렉토리 제거
    removeAllowedDirectory(index) {
        if (index >= 0 && index < this.allowedDirectories.length) {
            this.allowedDirectories.splice(index, 1);
            this.renderAllowedDirectories();
        }
    }

    // 기본 디렉토리로 설정
    makeDefaultDirectory(index) {
        if (index > 0 && index < this.allowedDirectories.length) {
            const directory = this.allowedDirectories[index];
            this.allowedDirectories.splice(index, 1);
            this.allowedDirectories.unshift(directory);
            this.renderAllowedDirectories();
        }
    }
}

// Initialize settings manager when DOM is loaded
let settingsManager;

function initializeSettings() {
    console.log('Initializing settings manager...');
    try {
        settingsManager = new SettingsManager();
        console.log('Settings manager created successfully');
    } catch (error) {
        console.error('Failed to create settings manager:', error);
    }
}

// DOM 로드 완료 후 초기화
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSettings);
} else {
    // DOM이 이미 로드된 경우 즉시 실행
    initializeSettings();
}

// 전역 함수로 추가 - HTML에서 직접 호출할 수 있도록
window.addAllowedDirectory = function() {
    if (settingsManager) {
        settingsManager.addAllowedDirectory();
    }
};

window.browseDirectory = function() {
    if (settingsManager) {
        settingsManager.browseDirectory();
    }
};

// LLM 설정 변경 처리 메서드들 추가
SettingsManager.prototype.onApiTypeChanged = function(newApiType) {
    console.log(`API type changed to: ${newApiType}`);

    // UI 업데이트
    this.updateApiTypeUI();

    // API 타입별 기본 설정 적용
    this.applyApiTypeDefaults(newApiType);

    // 즉시 LLM 설정 반영
    this.onLlmConfigChanged();
};

SettingsManager.prototype.applyApiTypeDefaults = function(apiType) {
    const apiUrlField = document.getElementById('apiUrl');
    const apiKeyField = document.getElementById('apiKey');
    const modelField = document.getElementById('modelName');

    // API 타입별 기본 설정
    const defaults = {
        local: {
            apiUrl: 'http://localhost:11434',
            model: 'llama4-maverick',
            requiresApiKey: false,
            description: 'Ollama 로컬 서버 (다중 엔드포인트 지원)'
        },
        llama: {
            apiUrl: 'http://localhost:8080',
            model: 'llama-2-7b-chat',
            requiresApiKey: false,
            description: 'LLaMA 서버 (completion, generate, chat 엔드포인트)'
        },
        openai: {
            apiUrl: 'https://api.openai.com',
            model: 'gpt-3.5-turbo',
            requiresApiKey: true,
            description: 'OpenAI API (chat/completions 엔드포인트)'
        },
        anthropic: {
            apiUrl: 'https://api.anthropic.com',
            model: 'claude-3-sonnet-20240229',
            requiresApiKey: true,
            description: 'Anthropic API (messages 엔드포인트)'
        },
        custom: {
            apiUrl: '',
            model: '',
            requiresApiKey: false,
            description: '사용자 정의 API (다중 엔드포인트 시도)'
        }
    };

    const config = defaults[apiType] || defaults.custom;

    // URL 필드 업데이트 (비어있는 경우만)
    if (apiUrlField && (!apiUrlField.value || apiUrlField.value === defaults[this.previousApiType]?.apiUrl)) {
        apiUrlField.value = config.apiUrl;
    }

    // 모델 필드 업데이트 (비어있는 경우만)
    if (modelField && (!modelField.value || modelField.value === defaults[this.previousApiType]?.model)) {
        modelField.value = config.model;
    }

    // API 키 필드 표시/숨김
    const apiKeyGroup = apiKeyField?.closest('.form-group');
    if (apiKeyGroup) {
        if (config.requiresApiKey) {
            apiKeyGroup.style.display = 'block';
            if (!apiKeyField.value) {
                this.showNotification(`${apiType.toUpperCase()} API 키를 입력해주세요.`, 'info');
            }
        } else {
            apiKeyGroup.style.display = 'none';
        }
    }

    // 설명 업데이트
    const descriptionElement = document.getElementById('apiTypeDescription');
    if (descriptionElement) {
        descriptionElement.textContent = config.description;
    }

    // 이전 API 타입 저장
    this.previousApiType = apiType;

    console.log(`Applied defaults for ${apiType}:`, config);
};

SettingsManager.prototype.onLlmConfigChanged = function() {
    console.log('LLM config changed, updating client...');

    // 디바운스된 업데이트 취소
    if (this.llmUpdateTimeout) {
        clearTimeout(this.llmUpdateTimeout);
    }

    // 즉시 LLM 클라이언트 업데이트
    this.llmUpdateTimeout = setTimeout(() => {
        this.updateLlmClient();
    }, 500); // 500ms 디바운스
};

SettingsManager.prototype.debouncedLlmConfigUpdate = function() {
    // 디바운스된 업데이트 (입력 중에 호출)
    if (this.llmInputTimeout) {
        clearTimeout(this.llmInputTimeout);
    }

    this.llmInputTimeout = setTimeout(() => {
        this.updateLlmClient();
    }, 1000); // 1초 디바운스
};

SettingsManager.prototype.updateLlmClient = async function() {
    try {
        console.log('Updating LLM client with new config...');

        // 현재 폼에서 LLM 설정 수집
        const llmConfig = {
            apiType: document.getElementById('apiType')?.value || 'local',
            apiUrl: document.getElementById('apiUrl')?.value || 'http://localhost:11434',
            customApiUrl: document.getElementById('customApiUrl')?.value || '',
            model: document.getElementById('modelName')?.value || 'llama4-maverick',
            temperature: parseFloat(document.getElementById('temperature')?.value || 0.7),
            maxTokens: parseInt(document.getElementById('maxTokens')?.value || 2048),
            apiKey: document.getElementById('apiKey')?.value || ''
        };

        console.log('New LLM config:', {
            ...llmConfig,
            apiKey: llmConfig.apiKey ? '***' : undefined
        });

        // 메인 프로세스에 LLM 설정 저장
        if (window.electronAPI && window.electronAPI.saveLlmConfig) {
            const result = await window.electronAPI.saveLlmConfig(llmConfig);
            if (result.success) {
                console.log('LLM config saved successfully');

                // 메인 앱에 설정 변경 알림 (있는 경우)
                if (window.electronAPI.notifyLlmConfigChanged) {
                    window.electronAPI.notifyLlmConfigChanged(llmConfig);
                }

                // 부모 창에 메시지 전송 (설정 창이 별도 창인 경우)
                if (window.opener && window.opener.postMessage) {
                    window.opener.postMessage({
                        type: 'llm-config-changed',
                        config: llmConfig
                    }, '*');
                }

            } else {
                console.error('Failed to save LLM config:', result.error);
            }
        }

    } catch (error) {
        console.error('Error updating LLM client:', error);
    }
};

// 메인 앱에서 LLM 설정 변경 메시지 수신 처리
window.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'llm-config-changed') {
        console.log('Received LLM config change notification:', event.data.config);
        // 필요한 경우 추가 처리
    }
});
