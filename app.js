// MCP Desktop Clone - 메인 애플리케이션 로직
class MCPDesktopApp {
    constructor() {
        this.currentChatId = null;
        this.chats = new Map();
        this.settings = this.loadSettings();
        this.attachedFiles = [];
        this.isConnected = false;
        this.mcpTestInProgress = false;

        this.init();
    }

    // 초기화
    init() {
        console.log('Initializing app...');
        
        // LLMClient 클래스 확인
        console.log('LLMClient available:', !!window.LLMClient);
        if (window.LLMClient) {
            console.log('LLMClient type:', typeof window.LLMClient);
        } else {
            console.error('LLMClient is not available in window object');
        }
        
        // 로딩 애니메이션 상태 초기화
        this.loadingAnimationInterval = null;
        
        // DOM이 완전히 로드된 후에 이벤트 리스너 설정
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('DOM fully loaded, setting up event listeners');
            this.setupEventListeners();

            // 로딩 오버레이 초기 상태 설정
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.classList.remove('active');
            }

            // DOM 로드 후 다시 LLMClient 확인
            console.log('LLMClient after DOM load:', !!window.LLMClient);

            // LLM 클라이언트 초기화
            await this.initializeLLMClient();

            // 채팅 목록 즉시 렌더링
            this.renderChatList();

            // 마지막 채팅이 있으면 선택
            this.selectLastActiveChat();
        });
        
        // DOM이 이미 로드된 상태라면 바로 실행
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('DOM already loaded, setting up event listeners immediately');
            this.setupEventListeners();
            this.renderChatList();
            this.selectLastActiveChat();
        }
        
        this.setupElectronListeners();
        this.setupLlmConfigListener();
        this.loadChats();
        this.applySettings();
        this.checkConnection();
        
        // 플랫폼별 초기화
        if (window.platform?.isMac) {
            document.body.classList.add('platform-mac');
        } else if (window.platform?.isWindows) {
            document.body.classList.add('platform-windows');
        }
    }

    // 마지막 활성 채팅 선택
    selectLastActiveChat() {
        // 채팅이 없으면 리턴
        if (this.chats.size === 0) {
            console.log('No chats available to select');
            return;
        }
        
        console.log('Selecting last active chat');
        
        // 마지막으로 업데이트된 채팅 찾기
        let lastChat = null;
        let lastUpdateTime = 0;
        
        this.chats.forEach(chat => {
            const updateTime = new Date(chat.updatedAt).getTime();
            if (updateTime > lastUpdateTime) {
                lastUpdateTime = updateTime;
                lastChat = chat;
            }
        });
        
        // 마지막 채팅이 있으면 선택
        if (lastChat) {
            console.log(`Selecting last active chat: ${lastChat.id} - ${lastChat.title}`);
            this.currentChatId = lastChat.id;
            this.showChatScreen();
            this.renderMessages();
        } else {
            console.log('No last chat found, showing welcome screen');
            this.showWelcomeScreen();
        }
    }

    // 이벤트 리스너 설정
    setupEventListeners() {
        console.log('Setting up event listeners...');
        
        // 타이틀바 컨트롤
        const minimizeBtn = document.getElementById('minimize-btn');
        const maximizeBtn = document.getElementById('maximize-btn');
        const closeBtn = document.getElementById('close-btn');

        if (minimizeBtn) {
            minimizeBtn.addEventListener('click', async () => {
                console.log('Minimize button clicked');
                if (window.electronAPI) {
                    await window.electronAPI.minimizeWindow();
                }
            });
        }
        
        if (maximizeBtn) {
            maximizeBtn.addEventListener('click', async () => {
                console.log('Maximize button clicked');
                if (window.electronAPI) {
                    await window.electronAPI.maximizeWindow();
                }
            });
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', async () => {
                console.log('Close button clicked');
                if (window.electronAPI) {
                    await window.electronAPI.closeWindow();
                }
            });
        }

        // 앱 로고 클릭 시 초기 화면으로 이동
        const appLogo = document.querySelector('.app-logo');
        const appTitle = document.querySelector('.app-title');
        
        if (appLogo) {
            appLogo.addEventListener('click', () => {
                console.log('App logo clicked, returning to welcome screen');
                this.currentChatId = null;
                this.showWelcomeScreen();
            });
            appLogo.style.cursor = 'pointer'; // 클릭 가능함을 시각적으로 표시
        }
        
        if (appTitle) {
            appTitle.addEventListener('click', () => {
                console.log('App title clicked, returning to welcome screen');
                this.currentChatId = null;
                this.showWelcomeScreen();
            });
            appTitle.style.cursor = 'pointer'; // 클릭 가능함을 시각적으로 표시
        }

        // 사이드바
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const newChatBtn = document.getElementById('new-chat-btn');
        const settingsBtn = document.getElementById('settings-btn');

        if (sidebarToggle) sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        if (newChatBtn) newChatBtn.addEventListener('click', () => this.createNewChat());
        if (settingsBtn) settingsBtn.addEventListener('click', () => this.openSettings());

        // 채팅 목록 이벤트는 renderChatList 메서드에서 처리됩니다
        this.renderChatList();

        // 메시지 입력
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');
        const attachBtn = document.getElementById('attach-btn');

        if (messageInput) {
            messageInput.addEventListener('input', (e) => this.handleInputChange(e));
            messageInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
        }
        if (sendBtn) sendBtn.addEventListener('click', () => this.sendMessage());
        if (attachBtn) attachBtn.addEventListener('click', () => this.attachFile());

        // 빠른 액션
        const quickActionBtns = document.querySelectorAll('.quick-action-btn');
        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const prompt = e.currentTarget.dataset.prompt;
                if (prompt) {
                    this.startChatWithPrompt(prompt);
                }
            });
        });

        // MCP 테스트 버튼
        const mcpTestBtn = document.getElementById('mcp-test-btn');
        if (mcpTestBtn) {
            mcpTestBtn.addEventListener('click', () => this.testMcpConnection());
        }

        // MCP 토글 버튼
        const mcpToggleBtn = document.getElementById('mcp-toggle-btn');
        if (mcpToggleBtn) {
            mcpToggleBtn.addEventListener('click', () => this.toggleMcpEnabled());
        }

        // 채팅 액션
        const exportBtn = document.getElementById('export-btn');
        const clearBtn = document.getElementById('clear-btn');

        if (exportBtn) exportBtn.addEventListener('click', () => this.exportChat());
        if (clearBtn) clearBtn.addEventListener('click', () => this.clearChat());

        // 설정 모달
        const settingsModal = document.getElementById('settings-modal');

        // 모달 배경 클릭
        if (settingsModal) {
            settingsModal.addEventListener('click', (e) => {
                if (e.target === settingsModal) {
                    this.closeSettings();
                }
            });
        }

        // 파일 입력
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }

        // Temperature 슬라이더
        const temperatureSlider = document.getElementById('temperature');
        const temperatureValue = document.getElementById('temperature-value');
        if (temperatureSlider && temperatureValue) {
            temperatureSlider.addEventListener('input', (e) => {
                temperatureValue.textContent = e.target.value;
            });
        }

        // 앱 상태 복원
        this.restoreAppState();
        
        // 페이지 언로드 시 앱 상태 저장
        window.addEventListener('beforeunload', () => {
            this.saveAppState();
        });
        
        // Electron API가 있는 경우 창 닫기 이벤트 처리
        if (window.electronAPI) {
            window.electronAPI.onBeforeClose(() => {
                this.saveAppState();
            });
        }
    }

    // Electron 이벤트 리스너
    setupElectronListeners() {
        if (window.electronAPI) {
            window.electronAPI.onNewChat(() => this.createNewChat());
            window.electronAPI.onExportChat(() => this.exportChat());
            window.electronAPI.onOpenSettings(() => this.openSettings());
            window.electronAPI.onToggleSidebar(() => this.toggleSidebar());

            // 윈도우 상태 변경 이벤트 리스너
            window.electronAPI.onWindowMaximized(() => {
                const maximizeBtn = document.getElementById('maximize-btn');
                if (maximizeBtn) {
                    maximizeBtn.innerHTML = '<i class="fas fa-window-restore"></i>';
                    maximizeBtn.title = '복원';
                }
            });

            window.electronAPI.onWindowUnmaximized(() => {
                const maximizeBtn = document.getElementById('maximize-btn');
                if (maximizeBtn) {
                    maximizeBtn.innerHTML = '<i class="fas fa-window-maximize"></i>';
                    maximizeBtn.title = '최대화';
                }
            });
        }
    }

    // LLM 설정 변경 리스너
    setupLlmConfigListener() {
        if (window.electronAPI && window.electronAPI.onLlmConfigChanged) {
            // LLM 설정 변경 이벤트 리스너 등록
            window.electronAPI.onLlmConfigChanged((newConfig) => {
                console.log('Received LLM config change:', newConfig);
                this.onLlmConfigChanged(newConfig);
            });
        }
    }

    // LLM 설정 변경 처리
    async onLlmConfigChanged(newConfig) {
        try {
            console.log('Processing LLM config change:', {
                ...newConfig,
                apiKey: newConfig.apiKey ? '***' : undefined
            });

            // API 타입별 특별 처리
            await this.handleApiTypeSpecificConfig(newConfig);

            // LLM 클라이언트 재초기화
            if (this.llmClient) {
                console.log('Updating existing LLM client config');
                this.llmClient.updateConfig(newConfig);
            } else {
                console.log('Initializing new LLM client with updated config');
                await this.initializeLLMClient();
            }

            // 대화창 상단의 모델명 업데이트
            this.updateModelDisplay(newConfig);

            // 연결 상태 재확인
            await this.checkConnection();

            console.log('LLM config change processed successfully');

        } catch (error) {
            console.error('Error processing LLM config change:', error);
        }
    }

    // 대화창 상단의 모델명 업데이트
    updateModelDisplay(config) {
        try {
            const modelInfoElement = document.querySelector('.model-info');
            if (modelInfoElement && config) {
                const apiType = config.apiType || 'local';
                const model = config.model || 'Unknown Model';

                // API 타입에 따른 표시 형식
                let displayText = '';
                switch (apiType.toLowerCase()) {
                    case 'local':
                        displayText = `${model} (Ollama)`;
                        break;
                    case 'llama':
                        displayText = `${model} (LLaMA Server)`;
                        break;
                    case 'openai':
                        displayText = `${model} (OpenAI)`;
                        break;
                    case 'anthropic':
                        displayText = `${model} (Anthropic)`;
                        break;
                    case 'custom':
                        displayText = `${model} (Custom)`;
                        break;
                    default:
                        displayText = model;
                }

                modelInfoElement.textContent = displayText;
                console.log(`Model display updated: ${displayText}`);
            }
        } catch (error) {
            console.error('Error updating model display:', error);
        }
    }

    // API 타입별 특별 설정 처리
    async handleApiTypeSpecificConfig(config) {
        const apiType = config.apiType?.toLowerCase();

        switch (apiType) {
            case 'local':
                console.log('Configuring for Ollama local server');
                // Ollama 서버 연결 확인
                await this.verifyLocalServerConnection(config.apiUrl);
                break;

            case 'llama':
                console.log('Configuring for LLaMA server');
                // LLaMA 서버 엔드포인트 확인
                await this.verifyLlamaServerEndpoints(config.apiUrl);
                break;

            case 'openai':
                console.log('Configuring for OpenAI API');
                // OpenAI API 키 검증
                if (config.apiKey) {
                    await this.verifyOpenAIApiKey(config.apiKey);
                }
                break;

            case 'anthropic':
                console.log('Configuring for Anthropic API');
                // Anthropic API 키 검증
                if (config.apiKey) {
                    await this.verifyAnthropicApiKey(config.apiKey);
                }
                break;

            case 'custom':
                console.log('Configuring for custom API');
                // 커스텀 API 엔드포인트 확인
                if (config.customApiUrl) {
                    await this.verifyCustomApiEndpoint(config.customApiUrl);
                }
                break;
        }
    }

    // 로컬 서버 연결 확인
    async verifyLocalServerConnection(apiUrl) {
        try {
            const endpoints = ['/v1/chat/completions', '/api/generate', '/completion'];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${apiUrl}${endpoint}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ model: 'test' })
                    });

                    console.log(`Local server endpoint ${endpoint}: ${response.status}`);
                } catch (error) {
                    console.log(`Local server endpoint ${endpoint} not available:`, error.message);
                }
            }
        } catch (error) {
            console.error('Error verifying local server connection:', error);
        }
    }

    // LLaMA 서버 엔드포인트 확인
    async verifyLlamaServerEndpoints(apiUrl) {
        try {
            const endpoints = ['/v1/chat/completions', '/completion', '/generate'];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${apiUrl}${endpoint}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ prompt: 'test' })
                    });

                    console.log(`LLaMA server endpoint ${endpoint}: ${response.status}`);
                } catch (error) {
                    console.log(`LLaMA server endpoint ${endpoint} not available:`, error.message);
                }
            }
        } catch (error) {
            console.error('Error verifying LLaMA server endpoints:', error);
        }
    }

    // OpenAI API 키 검증
    async verifyOpenAIApiKey(apiKey) {
        try {
            const response = await fetch('https://api.openai.com/v1/models', {
                headers: {
                    'Authorization': `Bearer ${apiKey}`
                }
            });

            console.log(`OpenAI API key verification: ${response.status}`);
        } catch (error) {
            console.error('Error verifying OpenAI API key:', error);
        }
    }

    // Anthropic API 키 검증
    async verifyAnthropicApiKey(apiKey) {
        try {
            // Anthropic API는 모델 목록 엔드포인트가 없으므로 간단한 메시지 테스트
            console.log('Anthropic API key configured (verification skipped)');
        } catch (error) {
            console.error('Error verifying Anthropic API key:', error);
        }
    }

    // 커스텀 API 엔드포인트 확인
    async verifyCustomApiEndpoint(customApiUrl) {
        try {
            const response = await fetch(customApiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ test: true })
            });

            console.log(`Custom API endpoint verification: ${response.status}`);
        } catch (error) {
            console.error('Error verifying custom API endpoint:', error);
        }
    }

    // LLM 클라이언트 초기화
    async initializeLLMClient() {
        try {
            console.log('Initializing LLM client...');

            // LLM 설정 가져오기
            let llmConfig;
            try {
                const configResult = await window.electronAPI?.getLlmConfig();
                console.log('LLM config result:', configResult?.success);

                if (configResult?.success) {
                    llmConfig = configResult.config;
                    console.log('LLM config loaded:', {
                        ...llmConfig,
                        apiKey: llmConfig.apiKey ? '***' : undefined
                    });
                } else {
                    throw new Error('LLM 설정을 가져올 수 없습니다.');
                }
            } catch (configError) {
                console.error('Failed to get LLM config:', configError);
                // 기본 설정 사용
                llmConfig = {
                    apiType: 'local',
                    apiUrl: 'http://localhost:11434',
                    model: 'llama4-maverick',
                    temperature: 0.7,
                    maxTokens: 2048
                };
            }

            // LLM 클라이언트 생성
            this.llmClient = new window.LLMClient(llmConfig);
            console.log('LLM client initialized successfully');

            // MCP 매니저 설정 (가능한 경우)
            if (window.electronAPI && window.electronAPI.mcpClientGetStatus) {
                try {
                    console.log('🔍 Checking MCP server status...');
                    const mcpStatus = await window.electronAPI.mcpClientGetStatus();
                    console.log('📊 MCP status check result:', mcpStatus);

                    if (mcpStatus.success && Object.keys(mcpStatus.status).length > 0) {
                        console.log('🎯 MCP servers found:', Object.keys(mcpStatus.status));
                        console.log('✅ MCP integration enabled for LLM client');
                        console.log('📋 MCP server status:', mcpStatus.status);

                        // MCP 매니저 프록시 객체 생성
                        const mcpManagerProxy = {
                            getAllAvailableTools: async () => {
                                const result = await window.electronAPI.mcpClientGetTools();
                                return result.success ? result.tools : [];
                            },
                            callTool: async (toolName, args) => {
                                const result = await window.electronAPI.mcpClientCallTool(toolName, args);
                                if (result.success) {
                                    return result.result;
                                } else {
                                    throw new Error(result.error);
                                }
                            }
                        };

                        this.llmClient.setMcpManager(mcpManagerProxy);
                        this.llmClient.setMcpEnabled(true);
                        console.log('MCP manager proxy set for LLM client');

                        // MCP 도구 목록 확인
                        mcpManagerProxy.getAllAvailableTools().then(tools => {
                            console.log('Available MCP tools after setup:', tools);
                            if (tools.length > 0) {
                                console.log('✅ MCP integration is working! Available tools:', tools.map(t => t.name));
                            } else {
                                console.warn('⚠️ No MCP tools available');
                            }
                        }).catch(error => {
                            console.error('❌ Failed to get MCP tools:', error);
                        });
                    } else {
                        console.warn('⚠️ No MCP servers available, MCP integration disabled');
                        console.log('📝 MCP status details:', mcpStatus);
                        this.llmClient.setMcpEnabled(false);
                    }
                } catch (error) {
                    console.error('❌ Failed to enable MCP integration:', error);
                    this.llmClient.setMcpEnabled(false);
                }
            } else {
                console.warn('⚠️ MCP client API not available - electronAPI missing');
                this.llmClient.setMcpEnabled(false);
            }

            // 대화창 상단의 모델명 업데이트
            this.updateModelDisplay(llmConfig);

            // 연결 상태 확인
            await this.checkConnection();

        } catch (error) {
            console.error('Failed to initialize LLM client:', error);

            // 폴백: 임시 LLM 클라이언트 생성
            const fallbackConfig = {
                apiType: 'local',
                apiUrl: 'http://localhost:11434',
                model: 'llama4-maverick',
                temperature: 0.7,
                maxTokens: 2048
            };

            this.llmClient = new TempLLMClient(fallbackConfig);
            console.log('Using temporary LLM client as fallback');

            // 폴백 클라이언트에도 모델명 업데이트
            this.updateModelDisplay(fallbackConfig);
        }
    }

    // 설정 로드
    loadSettings() {
        const defaultSettings = {
            apiEndpoint: 'http://localhost:11434',
            modelName: 'llama4-maverick',
            temperature: 0.7,
            darkMode: true,
            autoHideSidebar: false,
            mcpEnabled: false,
            mcpPort: 3000
        };

        try {
            const saved = localStorage.getItem('mcp-desktop-settings');
            return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
        } catch (error) {
            console.error('Failed to load settings:', error);
            return defaultSettings;
        }
    }

    // 설정 저장
    saveSettingsToStorage() {
        try {
            localStorage.setItem('mcp-desktop-settings', JSON.stringify(this.settings));
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    }

    // 설정 적용
    applySettings() {
        // 다크 모드
        document.documentElement.setAttribute('data-theme', this.settings.darkMode ? 'dark' : 'light');
        
        // 설정 폼 업데이트
        this.updateSettingsForm();
    }

    // 설정 폼 업데이트
    updateSettingsForm() {
        const elements = {
            'api-endpoint': this.settings.apiEndpoint,
            'model-name': this.settings.modelName,
            'temperature': this.settings.temperature,
            'dark-mode': this.settings.darkMode,
            'auto-hide-sidebar': this.settings.autoHideSidebar,
            'mcp-enabled': this.settings.mcpEnabled,
            'mcp-port': this.settings.mcpPort
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value;
                } else {
                    element.value = value;
                }
            }
        });

        // Temperature 값 표시
        const temperatureValue = document.getElementById('temperature-value');
        if (temperatureValue) {
            temperatureValue.textContent = this.settings.temperature;
        }
    }

    // 연결 상태 확인
    async checkConnection() {
        try {
            const response = await fetch(`${this.settings.apiEndpoint}/api/tags`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });
            
            this.isConnected = response.ok;
            this.updateConnectionStatus();
        } catch (error) {
            this.isConnected = false;
            this.updateConnectionStatus();
            console.error('Connection check failed:', error);
        }
    }

    // 연결 상태 업데이트
    updateConnectionStatus() {
        const indicator = document.querySelector('.status-indicator');
        if (indicator) {
            indicator.className = `status-indicator ${this.isConnected ? 'online' : 'offline'}`;
        }
    }

    // 채팅 로드
    loadChats() {
        try {
            const saved = localStorage.getItem('mcp-desktop-chats');
            if (saved) {
                const chatsData = JSON.parse(saved);
                Object.entries(chatsData).forEach(([id, chat]) => {
                    this.chats.set(id, chat);
                });
                console.log(`Loaded ${this.chats.size} chats from localStorage`);
                
                // 채팅 목록 즉시 렌더링
                this.renderChatList();
                
                // 채팅이 있으면 마지막 채팅 선택
                if (this.chats.size > 0) {
                    // 마지막 선택된 채팅 ID 가져오기
                    const lastChatId = localStorage.getItem('mcp-desktop-last-chat-id');
                    
                    if (lastChatId && this.chats.has(lastChatId)) {
                        // 마지막 선택된 채팅이 있으면 선택
                        console.log(`Selecting last selected chat: ${lastChatId}`);
                        this.currentChatId = lastChatId;
                        this.showChatScreen();
                    } else {
                        // 없으면 마지막 업데이트된 채팅 선택
                        this.selectLastActiveChat();
                    }
                }
            } else {
                console.log('No saved chats found');
                this.showWelcomeScreen();
            }
        } catch (error) {
            console.error('Failed to load chats:', error);
            this.showWelcomeScreen();
        }
    }

    // 채팅 저장
    saveChats() {
        try {
            const chatsData = Object.fromEntries(this.chats);
            localStorage.setItem('mcp-desktop-chats', JSON.stringify(chatsData));
        } catch (error) {
            console.error('Failed to save chats:', error);
        }
    }

    // 새 채팅 생성
    createNewChat() {
        const chatId = this.generateId();
        const chat = {
            id: chatId,
            title: '새 채팅',
            messages: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.chats.set(chatId, chat);
        this.currentChatId = chatId;
        this.saveChats();
        this.renderChatList();
        this.showChatScreen();
        this.renderMessages();

        // 메시지 입력에 포커스
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.focus();
        }
    }

    // 채팅 선택
    selectChat(chatId) {
        this.currentChatId = chatId;
        this.renderChatList();
        this.showChatScreen();
        this.renderMessages();
    }

    // 프롬프트로 채팅 시작
    startChatWithPrompt(prompt) {
        if (!this.currentChatId) {
            this.createNewChat();
        }
        
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.value = prompt;
            messageInput.focus();
            this.handleInputChange({ target: messageInput });
        }
    }

    // 채팅 화면 표시
    showChatScreen() {
        const welcomeScreen = document.getElementById('welcome-screen');
        const chatScreen = document.getElementById('chat-screen');

        if (welcomeScreen) welcomeScreen.style.display = 'none';
        if (chatScreen) chatScreen.style.display = 'flex';
        
        // 채팅 화면이 표시될 때 메시지 컨테이너 스크롤을 아래로
        setTimeout(() => {
            this.scrollToBottom();
        }, 100);
    }

    // 환영 화면 표시
    showWelcomeScreen() {
        const welcomeScreen = document.getElementById('welcome-screen');
        const chatScreen = document.getElementById('chat-screen');

        if (welcomeScreen) welcomeScreen.style.display = 'flex';
        if (chatScreen) chatScreen.style.display = 'none';
        
        // 현재 채팅 ID 초기화
        this.currentChatId = null;
        
        // 채팅 목록 업데이트
        this.renderChatList();
    }

    // 채팅 목록 렌더링 함수 수정 - 아이콘 제거 및 1열 정렬
    renderChatList() {
        const chatItems = document.getElementById('chat-items');
        if (!chatItems) {
            console.error('Chat items container not found');
            return;
        }

        console.log('Rendering chat list with', this.chats.size, 'chats');
        
        // 기존 이벤트 리스너 제거를 위해 내용 비우기
        chatItems.innerHTML = '';

        // 채팅 정렬
        const sortedChats = Array.from(this.chats.values())
            .sort((a, b) => {
                // 즐겨찾기 항목 먼저 정렬
                if (a.favorite && !b.favorite) return -1;
                if (!a.favorite && b.favorite) return 1;
                // 그 다음 최신순 정렬
                return new Date(b.updatedAt) - new Date(a.updatedAt);
            });

        // 각 채팅 항목 추가
        sortedChats.forEach(chat => {
            const isActive = chat.id === this.currentChatId;
            const isFavorite = chat.favorite === true;
            const time = this.formatTime(new Date(chat.updatedAt));

            // 채팅 항목 요소 생성
            const chatItem = document.createElement('div');
            chatItem.className = `chat-item ${isActive ? 'active' : ''} ${isFavorite ? 'favorite' : ''}`;
            chatItem.dataset.chatId = chat.id;
            
            // 채팅 항목 내용 설정 - 아이콘 제거, 제목만 표시
            chatItem.innerHTML = `
                <div class="chat-item-content">
                    <div class="chat-item-title">${this.escapeHtml(chat.title)}</div>
                </div>
                <div class="chat-item-time">${time}</div>
                <button class="chat-item-menu" data-chat-id="${chat.id}" title="메뉴">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="chat-item-dropdown" id="dropdown-${chat.id}">
                    <div class="dropdown-item favorite-btn" data-chat-id="${chat.id}">
                        <i class="fas ${isFavorite ? 'fa-star' : 'fa-star-o'}"></i>
                        ${isFavorite ? '즐겨찾기 해제' : '즐겨찾기'}
                    </div>
                    <div class="dropdown-item rename-btn" data-chat-id="${chat.id}">
                        <i class="fas fa-edit"></i>
                        이름 변경
                    </div>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-item delete-btn" data-chat-id="${chat.id}">
                        <i class="fas fa-trash"></i>
                        삭제
                    </div>
                </div>
            `;
            
            // 채팅 항목 클릭 이벤트
            chatItem.addEventListener('click', (e) => {
                // 메뉴 버튼이나 드롭다운 항목 클릭 시 채팅 선택 방지
                if (e.target.closest('.chat-item-menu') || e.target.closest('.chat-item-dropdown')) {
                    return;
                }
                
                this.selectChat(chat.id);
            });
            
            // 메뉴 버튼 클릭 이벤트
            const menuBtn = chatItem.querySelector('.chat-item-menu');
            if (menuBtn) {
                menuBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const dropdown = chatItem.querySelector('.chat-item-dropdown');
                    
                    // 다른 열린 드롭다운 닫기
                    document.querySelectorAll('.chat-item-dropdown.show').forEach(el => {
                        if (el !== dropdown) {
                            el.classList.remove('show');
                        }
                    });
                    
                    // 현재 드롭다운 토글
                    dropdown.classList.toggle('show');
                });
            }
            
            // 즐겨찾기 버튼 이벤트
            const favoriteBtn = chatItem.querySelector('.favorite-btn');
            if (favoriteBtn) {
                favoriteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log('Favorite button clicked for chat:', chat.id);
                    this.toggleFavorite(chat.id);
                });
            }
            
            // 이름 변경 버튼 이벤트
            const renameBtn = chatItem.querySelector('.rename-btn');
            if (renameBtn) {
                renameBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log('Rename button clicked for chat:', chat.id);
                    this.renameChat(chat.id);
                });
            }
            
            // 삭제 버튼 이벤트
            const deleteBtn = chatItem.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log('Delete button clicked for chat:', chat.id);
                    this.deleteChat(chat.id);
                });
            }
            
            // 채팅 항목 추가
            chatItems.appendChild(chatItem);
        });
    }

    // 즐겨찾기 토글
    toggleFavorite(chatId) {
        const chat = this.chats.get(chatId);
        if (chat) {
            chat.favorite = !chat.favorite;
            chat.updatedAt = new Date().toISOString();
            this.saveChats();
            this.renderChatList();
        }
    }

    // 채팅 이름 변경
    renameChat(chatId) {
        const chat = this.chats.get(chatId);
        if (chat) {
            const newTitle = prompt('채팅 이름을 입력하세요:', chat.title);
            if (newTitle && newTitle.trim() !== '') {
                chat.title = newTitle.trim();
                chat.updatedAt = new Date().toISOString();
                this.saveChats();
                this.renderChatList();
                
                // 현재 선택된 채팅이면 제목 업데이트
                if (this.currentChatId === chatId) {
                    const currentChatTitle = document.getElementById('current-chat-title');
                    if (currentChatTitle) {
                        currentChatTitle.textContent = chat.title;
                    }
                }
            }
        }
    }

    // 채팅 삭제
    deleteChat(chatId) {
        if (confirm('이 채팅을 삭제하시겠습니까?')) {
            this.chats.delete(chatId);
            this.saveChats();
            
            if (this.currentChatId === chatId) {
                this.currentChatId = null;
                this.showWelcomeScreen();
            }
            
            this.renderChatList();
        }
    }

    // 메시지 렌더링
    renderMessages() {
        const messagesContainer = document.getElementById('messages');
        const currentChatTitle = document.getElementById('current-chat-title');
        
        if (!messagesContainer || !this.currentChatId) return;

        const chat = this.chats.get(this.currentChatId);
        if (!chat) return;

        // 채팅 제목 업데이트
        if (currentChatTitle) {
            currentChatTitle.textContent = chat.title;
        }

        // 메시지 렌더링
        messagesContainer.innerHTML = chat.messages.map(message => {
            const isUser = message.role === 'user';
            const avatar = isUser ? 'U' : 'AI';
            const time = this.formatTime(new Date(message.timestamp));

            return `
                <div class="message ${isUser ? 'user' : 'assistant'}">
                    <div class="message-avatar">${avatar}</div>
                    <div class="message-content">
                        <div class="message-bubble">
                            ${this.formatMessageContent(message.content)}
                        </div>
                        <div class="message-time">${time}</div>
                    </div>
                </div>
            `;
        }).join('');

        // 스크롤을 맨 아래로
        this.scrollToBottom();
    }

    // 메시지 전송
    async sendMessage() {
        const messageInput = document.getElementById('message-input');
        const content = messageInput?.value.trim();

        if (!content && this.attachedFiles.length === 0) return;
        if (!this.currentChatId) {
            this.createNewChat();
        }

        const chat = this.chats.get(this.currentChatId);
        if (!chat) return;

        // 사용자 메시지 추가
        const userMessage = {
            role: 'user',
            content: content || '[파일 첨부]',
            timestamp: new Date().toISOString(),
            files: [...this.attachedFiles]
        };

        chat.messages.push(userMessage);
        
        // 채팅 제목 자동 생성 (첫 메시지인 경우)
        if (chat.messages.length === 1 && content) {
            chat.title = content.substring(0, 30) + (content.length > 30 ? '...' : '');
        }

        chat.updatedAt = new Date().toISOString();
        this.saveChats();
        this.renderChatList();
        this.renderMessages();

        // 입력 필드 초기화
        if (messageInput) {
            messageInput.value = '';
            messageInput.style.height = 'auto';
        }
        this.attachedFiles = [];
        this.updateSendButton();

        // AI 응답 요청
        await this.getAIResponse(chat);
    }

    // API 키 가져오기 (암호화 및 직접 저장 모두 시도)
    async getApiKey() {
        try {
            console.log('Getting API key...');
            
            // 0. 로컬 스토리지에서 먼저 확인
            const localApiKey = localStorage.getItem('openai-api-key');
            if (localApiKey) {
                console.log('API key found in localStorage');
                return localApiKey;
            }
            
            // 1. 암호화 저장에서 가져오기 시도
            const result = await window.electronAPI.getApiKey();
            console.log('API key result:', result?.success);
            
            if (result?.success && result.apiKey) {
                console.log('API key retrieved successfully from encrypted storage');
                // 로컬 스토리지에도 저장
                localStorage.setItem('openai-api-key', result.apiKey);
                return result.apiKey;
            }
            
            // 2. 직접 저장에서 가져오기 시도
            const directResult = await window.electronAPI.getApiKeyDirect();
            console.log('API key direct result:', directResult?.success);
            
            if (directResult?.success && directResult.apiKey) {
                console.log('API key retrieved successfully from direct storage');
                // 로컬 스토리지에도 저장
                localStorage.setItem('openai-api-key', directResult.apiKey);
                return directResult.apiKey;
            }
            
            // 3. 사용자에게 직접 요청
            const promptApiKey = prompt('OpenAI API 키를 입력해주세요:', '');
            if (promptApiKey) {
                console.log('API key provided by user prompt');
                // 로컬 스토리지에 저장
                localStorage.setItem('openai-api-key', promptApiKey);
                // 가능하면 암호화 저장소에도 저장
                try {
                    await window.electronAPI.saveApiKey(promptApiKey);
                    console.log('API key saved to encrypted storage');
                } catch (saveError) {
                    console.error('Failed to save API key to encrypted storage:', saveError);
                }
                return promptApiKey;
            }
            
            console.error('Failed to get API key from all storage methods');
            return '';
        } catch (error) {
            console.error('Error getting API key:', error);
            
            // 4. 오류 발생 시 사용자에게 직접 요청
            const promptApiKey = prompt('OpenAI API 키를 입력해주세요 (오류 발생):', '');
            if (promptApiKey) {
                console.log('API key provided by user prompt after error');
                // 로컬 스토리지에 저장
                localStorage.setItem('openai-api-key', promptApiKey);
                return promptApiKey;
            }
            
            return '';
        }
    }

    // AI 응답 받기
    async getAIResponse(chat) {
        this.showLoading(true);
        this.scrollToBottom(); // 로딩 시작 시 스크롤을 맨 아래로

        try {
            // LLM 설정 가져오기
            let llmConfig;
            let configResult;

            try {
                // 1. LLM 설정 가져오기
                configResult = await window.electronAPI?.getLlmConfig();
                console.log('LLM config result:', configResult?.success);

                if (configResult?.success) {
                    llmConfig = configResult.config;
                    console.log('LLM config type:', llmConfig.apiType);
                    console.log('API key exists in config:', !!llmConfig.apiKey);

                    // 직접 저장된 API 키 확인
                    if ((llmConfig.apiType === 'openai' || llmConfig.apiType === 'anthropic' || llmConfig.apiType === 'custom')
                        && !llmConfig.apiKey && llmConfig.directApiKey) {
                        console.log('Using direct API key from config');
                        llmConfig.apiKey = llmConfig.directApiKey;
                    }
                } else {
                    throw new Error('LLM 설정을 가져올 수 없습니다.');
                }

                // 2. API 키가 여전히 없으면 명시적으로 가져오기 시도
                if ((llmConfig.apiType === 'openai' || llmConfig.apiType === 'anthropic' || llmConfig.apiType === 'custom')
                    && !llmConfig.apiKey) {
                    console.log('API key not in config, trying to get it explicitly');

                    try {
                        // 1. 로컬 스토리지에서 먼저 시도
                        const storageKey = llmConfig.apiType === 'openai' ? 'openai-api-key' :
                                         llmConfig.apiType === 'anthropic' ? 'anthropic-api-key' :
                                         'custom-api-key';
                        const localApiKey = localStorage.getItem(storageKey);

                        if (localApiKey) {
                            llmConfig.apiKey = localApiKey;
                            console.log(`API key retrieved from localStorage (${storageKey})`);
                        } else {
                            // 2. 암호화된 API 키 가져오기 시도
                            try {
                                if (window.electronAPI && window.electronAPI.getApiKey) {
                                    const apiKeyResult = await window.electronAPI.getApiKey();
                                    console.log('Encrypted API key result:', apiKeyResult?.success);

                                    if (apiKeyResult?.success && apiKeyResult.apiKey) {
                                        llmConfig.apiKey = apiKeyResult.apiKey;
                                        console.log('API key retrieved successfully from encrypted storage');
                                        // 로컬 스토리지에도 저장
                                        localStorage.setItem(storageKey, apiKeyResult.apiKey);
                                    } else {
                                        console.warn('No encrypted API key found');
                                        throw new Error(`${llmConfig.apiType.toUpperCase()} API 키가 설정되지 않았습니다. 설정에서 API 키를 입력해주세요.`);
                                    }
                                } else {
                                    console.warn('electronAPI.getApiKey not available');
                                    throw new Error(`${llmConfig.apiType.toUpperCase()} API 키가 설정되지 않았습니다. 설정에서 API 키를 입력해주세요.`);
                                }
                            } catch (apiKeyError) {
                                console.error('Error getting API key:', apiKeyError);
                                throw new Error(`${llmConfig.apiType.toUpperCase()} API 키가 설정되지 않았습니다. 설정에서 API 키를 입력해주세요.`);
                            }
                        }
                    } catch (keyRetrievalError) {
                        console.error('Failed to retrieve API key:', keyRetrievalError);
                        throw keyRetrievalError;
                    }
                }
            } catch (configError) {
                console.error('Failed to get LLM config:', configError);
                throw configError; // 오류를 상위로 전파
            }

            // LLM 클라이언트 생성 시 로깅 추가
            console.log('=== LLM Config Debug ===');
            console.log('Raw config from electronAPI:', configResult);
            console.log('Processed LLM config:', {
                apiType: llmConfig.apiType,
                apiUrl: llmConfig.apiUrl,
                model: llmConfig.model,
                temperature: llmConfig.temperature,
                maxTokens: llmConfig.maxTokens,
                hasApiKey: !!llmConfig.apiKey,
                fullConfig: llmConfig
            });

            // 설정 검증
            if (!llmConfig.apiType) {
                console.warn('API type is missing, using default');
                llmConfig.apiType = 'openai';
            }

            if (!llmConfig.apiUrl) {
                console.warn('API URL is missing, using default for API type');
                const defaults = {
                    openai: 'https://api.openai.com',
                    anthropic: 'https://api.anthropic.com',
                    ollama: 'http://localhost:11434',
                    llamacpp: 'http://localhost:8080',
                    custom: ''
                };
                llmConfig.apiUrl = defaults[llmConfig.apiType] || defaults.openai;
            }

            if (!llmConfig.model) {
                console.warn('Model is missing, using default for API type');
                const defaults = {
                    openai: 'gpt-3.5-turbo',
                    anthropic: 'claude-3-sonnet-20240229',
                    ollama: 'llama2',
                    llamacpp: 'model',
                    custom: 'model'
                };
                llmConfig.model = defaults[llmConfig.apiType] || defaults.openai;
            }

            console.log('Final LLM config after validation:', {
                apiType: llmConfig.apiType,
                apiUrl: llmConfig.apiUrl,
                model: llmConfig.model,
                temperature: llmConfig.temperature,
                maxTokens: llmConfig.maxTokens
            });

            // API 타입 검증
            if (!llmConfig.apiType) {
                console.error('API type is missing from config');
                throw new Error('API 타입이 설정되지 않았습니다.');
            }

            if (llmConfig.apiType === 'local' && !llmConfig.apiUrl) {
                console.error('API URL is missing for local server');
                throw new Error('로컬 서버 URL이 설정되지 않았습니다.');
            }

            if (!llmConfig.model) {
                console.error('Model is missing from config');
                throw new Error('모델이 설정되지 않았습니다.');
            }

            // LLM 클라이언트 생성
            const llmClient = new window.LLMClient(llmConfig);

            // 클라이언트 설정 확인
            console.log('LLM client created, verifying config:', {
                clientApiType: llmClient.config?.apiType,
                clientApiUrl: llmClient.config?.apiUrl,
                clientModel: llmClient.config?.model
            });

            // 프롬프트 설정 적용하여 메시지 형식 변환
            console.log('LLM config prompt settings:', llmConfig.promptSettings);
            const messages = this.applyPromptSettings(chat.messages, llmConfig.promptSettings);
            console.log('Messages after applying prompt settings:', messages);

            // AI 응답 메시지 추가 (빈 내용으로 시작)
            const aiMessage = {
                role: 'assistant',
                content: '',
                timestamp: new Date().toISOString()
            };

            chat.messages.push(aiMessage);
            this.saveChats();
            this.renderChatList();
            this.renderMessages();

            // 스트리밍 콜백 함수
            const onChunk = (chunk) => {
                // 메시지 내용 업데이트
                aiMessage.content += chunk;
                // 메시지 렌더링 업데이트
                this.updateLastMessage(aiMessage.content);
                // 스크롤 맨 아래로
                this.scrollToBottom();
            };

            // MCP 상태 확인 및 로깅
            console.log('🔍 Pre-request MCP status check:');
            console.log('- MCP enabled:', llmClient.mcpEnabled);
            console.log('- MCP manager available:', !!llmClient.mcpManager);

            if (llmClient.mcpManager) {
                try {
                    const tools = await llmClient.getAvailableMcpTools();
                    console.log('- Available MCP tools:', tools.length, tools.map(t => t.name));
                } catch (error) {
                    console.error('- Failed to get MCP tools:', error);
                }
            }

            // MCP 통합된 AI 응답 요청 (스트리밍 지원)
            if (llmClient.sendMessage && typeof llmClient.sendMessage === 'function') {
                // MCP 통합 메서드 사용 (논스트리밍)
                console.log('🚀 Using MCP-integrated sendMessage method');
                console.log('📝 Sending messages:', messages.length, 'messages');
                console.log('📋 First message preview:', messages[0]?.content?.substring(0, 100) + '...');

                const response = await llmClient.sendMessage(messages, {
                    temperature: llmConfig.temperature,
                    maxTokens: llmConfig.maxTokens
                });

                console.log('✅ Received response from LLM');
                console.log('📝 Response preview:', response.substring(0, 200) + '...');

                // 응답을 스트리밍처럼 표시
                aiMessage.content = response;
                this.updateLastMessage(response);
            } else {
                // 기존 스트리밍 방식 사용
                console.log('📡 Using traditional chatCompletionStream method');
                await llmClient.chatCompletionStream(messages, {
                    temperature: llmConfig.temperature,
                    maxTokens: llmConfig.maxTokens,
                    onChunk
                });
            }

            // 완료 후 최종 업데이트
            chat.updatedAt = new Date().toISOString();
            this.saveChats();
            this.renderChatList();
            this.scrollToBottom();

        } catch (error) {
            console.error('AI 응답 오류:', error);

            // 오류 메시지 추가
            const errorMessage = {
                role: 'assistant',
                content: `오류가 발생했습니다: ${error.message}`,
                timestamp: new Date().toISOString()
            };

            chat.messages.push(errorMessage);
            chat.updatedAt = new Date().toISOString();
            this.saveChats();
            this.renderMessages();
            this.scrollToBottom();
        } finally {
            this.showLoading(false);
        }
    }

    // 입력 변경 처리
    handleInputChange(e) {
        const input = e.target;
        
        // 자동 높이 조절
        input.style.height = 'auto';
        input.style.height = Math.min(input.scrollHeight, 120) + 'px';

        // 문자 수 업데이트
        const charCount = document.getElementById('char-count');
        if (charCount) {
            charCount.textContent = input.value.length;
        }

        // 전송 버튼 활성화/비활성화
        this.updateSendButton();
    }

    // 키 입력 처리
    handleKeyDown(e) {
        if (e.key === 'Enter') {
            if (e.shiftKey) {
                // Shift + Enter: 줄바꿈 (기본 동작)
                return;
            } else {
                // Enter: 메시지 전송
                e.preventDefault();
                this.sendMessage();
            }
        }
    }

    // 전송 버튼 상태 업데이트
    updateSendButton() {
        const sendBtn = document.getElementById('send-btn');
        const messageInput = document.getElementById('message-input');
        
        if (sendBtn && messageInput) {
            const hasContent = messageInput.value.trim().length > 0 || this.attachedFiles.length > 0;
            sendBtn.disabled = !hasContent;
        }
    }

    // 파일 첨부
    attachFile() {
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.click();
        }
    }

    // 파일 선택 처리
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        files.forEach(file => {
            if (file.size > 10 * 1024 * 1024) { // 10MB 제한
                alert(`파일 크기가 너무 큽니다: ${file.name}`);
                return;
            }
            
            this.attachedFiles.push({
                name: file.name,
                size: file.size,
                type: file.type,
                data: file
            });
        });

        this.updateSendButton();
        this.renderAttachments();
    }

    // 첨부파일 렌더링
    renderAttachments() {
        const attachmentContainer = document.getElementById('attachment-preview');
        if (!attachmentContainer) return;

        if (this.attachedFiles.length === 0) {
            attachmentContainer.style.display = 'none';
            return;
        }

        attachmentContainer.style.display = 'block';
        attachmentContainer.innerHTML = this.attachedFiles.map((file, index) => `
            <div class="attachment-item">
                <span class="attachment-name">${this.escapeHtml(file.name)}</span>
                <span class="attachment-size">${this.formatFileSize(file.size)}</span>
                <button class="attachment-remove" data-index="${index}">×</button>
            </div>
        `).join('');

        // 첨부파일 제거 이벤트
        attachmentContainer.querySelectorAll('.attachment-remove').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.dataset.index);
                this.attachedFiles.splice(index, 1);
                this.renderAttachments();
                this.updateSendButton();
            });
        });
    }

    // 사이드바 토글
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('collapsed');
        }
    }

    // 설정 열기
    openSettings() {
        const modal = document.getElementById('settings-modal');
        if (modal) {
            modal.classList.add('active');

            const closeModal = () => {
                modal.classList.remove('active');
                // 이벤트 리스너 정리
                window.removeEventListener('message', handleMessage);
                document.removeEventListener('keydown', handleEscape);
            };

            // 모달 외부 클릭 시 닫기
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // ESC 키로 닫기
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    closeModal();
                }
            };
            document.addEventListener('keydown', handleEscape);

            // iframe으로부터 메시지 수신
            const handleMessage = (event) => {
                if (event.data.type === 'settings-saved') {
                    closeModal();
                    // 설정이 저장되었으므로 필요한 경우 UI 업데이트
                    console.log('Settings saved from iframe');
                } else if (event.data.type === 'settings-cancelled') {
                    closeModal();
                    console.log('Settings cancelled from iframe');
                }
            };
            window.addEventListener('message', handleMessage);
        }
    }

    // 설정 닫기
    closeSettings() {
        const modal = document.getElementById('settings-modal');
        if (modal) {
            modal.classList.remove('active');
        }
    }



    // 채팅 내보내기
    async exportChat() {
        if (!this.currentChatId) return;

        const chat = this.chats.get(this.currentChatId);
        if (!chat) return;

        const exportData = {
            title: chat.title,
            createdAt: chat.createdAt,
            messages: chat.messages.map(msg => ({
                role: msg.role,
                content: msg.content,
                timestamp: msg.timestamp
            }))
        };

        const content = JSON.stringify(exportData, null, 2);
        const filename = `${chat.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.json`;

        if (window.electronAPI) {
            const result = await window.electronAPI.showSaveDialog({
                defaultPath: filename,
                filters: [
                    { name: 'JSON', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!result.canceled && result.filePath) {
                await window.electronAPI.writeFile(result.filePath, content);
            }
        } else {
            // 웹 버전 대체
            const blob = new Blob([content], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }

    // 채팅 지우기
    clearChat() {
        if (!this.currentChatId) return;

        if (confirm('현재 채팅의 모든 메시지를 삭제하시겠습니까?')) {
            const chat = this.chats.get(this.currentChatId);
            if (chat) {
                chat.messages = [];
                chat.title = '새 채팅';
                chat.updatedAt = new Date().toISOString();
                this.saveChats();
                this.renderChatList();
                this.renderMessages();
            }
        }
    }

    // 로딩 텍스트 애니메이션
    animateLoadingText() {
        if (!this.loadingAnimationInterval) {
            const loadingText = document.querySelector('.loading-text');
            if (!loadingText) return;
            
            let dots = 0;
            this.loadingAnimationInterval = setInterval(() => {
                dots = (dots + 1) % 4;
                loadingText.textContent = '잠시만 기다려 주세요.' + '.'.repeat(dots);
            }, 500);
        }
    }

    // 로딩 애니메이션 중지
    stopLoadingAnimation() {
        if (this.loadingAnimationInterval) {
            clearInterval(this.loadingAnimationInterval);
            this.loadingAnimationInterval = null;
        }
    }

    // 로딩 상태 표시
    showLoading(show) {
        console.log('showLoading called with:', show);

        const loadingOverlay = document.getElementById('loading-overlay');
        const messageLoadingIndicator = document.getElementById('message-loading-indicator');

        // 전체 화면 로딩 오버레이는 숨김 처리
        if (loadingOverlay) {
            loadingOverlay.classList.remove('active');
        }

        // 메시지 영역 내 로딩 인디케이터 표시
        if (messageLoadingIndicator) {
            messageLoadingIndicator.style.display = show ? 'flex' : 'none';

            // 로딩 시 스크롤을 맨 아래로
            if (show) {
                this.scrollToBottom();
            }
        }

        // 로딩 텍스트 애니메이션 관리
        if (show) {
            this.animateLoadingText();
        } else {
            this.stopLoadingAnimation();
        }

        // 전송 버튼 상태 관리
        const sendBtn = document.getElementById('send-btn');
        if (sendBtn) {
            if (show) {
                sendBtn.disabled = true;
                sendBtn.textContent = '처리 중...';
            } else {
                sendBtn.disabled = false;
                sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
                // 입력 내용에 따라 버튼 상태 재설정
                this.updateSendButton();
            }
        }

        // 메시지 입력 필드 비활성화
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.disabled = show;
            if (!show) {
                messageInput.focus(); // 로딩 완료 시 포커스 복원
            }
        }

        // 첨부 버튼 비활성화
        const attachBtn = document.getElementById('attach-btn');
        if (attachBtn) {
            attachBtn.disabled = show;
        }

        // MCP 테스트 버튼 비활성화
        const mcpTestBtn = document.getElementById('mcp-test-btn');
        if (mcpTestBtn) {
            mcpTestBtn.disabled = show;
        }

        console.log('Loading state updated:', show ? 'ON' : 'OFF');
    }

    // 스크롤을 맨 아래로
    scrollToBottom() {
        const messagesContainer = document.getElementById('messages-container');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    // 마지막 메시지 내용만 업데이트
    updateLastMessage(content) {
        const messagesContainer = document.getElementById('messages');
        if (!messagesContainer) return;
        
        // 마지막 메시지 요소 찾기
        const lastMessage = messagesContainer.querySelector('.message.assistant:last-child');
        if (lastMessage) {
            // 메시지 버블 내용 업데이트
            const messageBubble = lastMessage.querySelector('.message-bubble');
            if (messageBubble) {
                messageBubble.innerHTML = this.formatMessageContent(content);
            }
        }
    }

    // 유틸리티 함수들
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    formatTime(date) {
        const now = new Date();
        const diff = now - date;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (seconds < 60) return '방금 전';
        if (minutes < 60) return `${minutes}분 전`;
        if (hours < 24) return `${hours}시간 전`;
        if (days < 7) return `${days}일 전`;
        
        return date.toLocaleDateString('ko-KR', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatMessageContent(content) {
        // 마크다운 간단 변환 (링크, 볼드, 이탤릭 등)
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>')
            .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 앱 상태 저장
    saveAppState() {
        try {
            // 현재 채팅 ID 저장
            if (this.currentChatId) {
                localStorage.setItem('mcp-desktop-last-chat-id', this.currentChatId);
            }
            
            // 사이드바 상태 저장
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                const isSidebarCollapsed = sidebar.classList.contains('collapsed');
                localStorage.setItem('mcp-desktop-sidebar-collapsed', isSidebarCollapsed);
            }
            
            console.log('App state saved');
        } catch (error) {
            console.error('Failed to save app state:', error);
        }
    }

    // 앱 상태 복원
    restoreAppState() {
        try {
            // 사이드바 상태 복원
            const sidebarCollapsed = localStorage.getItem('mcp-desktop-sidebar-collapsed');
            if (sidebarCollapsed === 'true') {
                const sidebar = document.getElementById('sidebar');
                if (sidebar) {
                    sidebar.classList.add('collapsed');
                }
            }

            // MCP 토글 상태 초기화
            this.initializeMcpToggleState();

            console.log('App state restored');
        } catch (error) {
            console.error('Failed to restore app state:', error);
        }
    }

    // 프롬프트 설정을 메시지에 적용
    applyPromptSettings(chatMessages, promptSettings) {
        console.log('Applying prompt settings:', promptSettings);

        if (!promptSettings) {
            // 프롬프트 설정이 없으면 기본 변환
            return chatMessages.map(msg => ({
                role: msg.role,
                content: msg.content
            }));
        }

        const messages = [];

        // 1. 시스템 프롬프트 추가 (활성화된 경우)
        if (promptSettings.enableSystemPrompt && promptSettings.systemPrompt) {
            console.log('Adding system prompt:', promptSettings.systemPrompt);
            messages.push({
                role: 'system',
                content: promptSettings.systemPrompt
            });
        }

        // 2. 채팅 메시지 변환
        for (const msg of chatMessages) {
            if (msg.role === 'user' && promptSettings.enablePromptTemplate && promptSettings.userPromptTemplate) {
                // 사용자 메시지에 템플릿 적용
                const templatedContent = promptSettings.userPromptTemplate.replace('{user_input}', msg.content);
                console.log('Applied user prompt template:', templatedContent);
                messages.push({
                    role: msg.role,
                    content: templatedContent
                });
            } else {
                // 일반 메시지 변환
                messages.push({
                    role: msg.role,
                    content: msg.content
                });
            }
        }

        console.log('Final messages with prompt settings applied:', messages.length, 'messages');
        return messages;
    }

    // MCP 연결 테스트 메서드
    async testMcpConnection() {
        console.log('Testing MCP connection...');

        // 중복 실행 방지
        if (this.mcpTestInProgress) {
            console.log('MCP test already in progress, skipping...');
            return;
        }

        this.mcpTestInProgress = true;

        try {
            // 로딩 표시
            console.log('Showing loading indicator...');
            this.showLoading(true);

            // LLM 클라이언트 초기화
            if (!this.llmClient) {
                await this.initializeLLMClient();
            }

            if (!this.llmClient) {
                throw new Error('LLM 클라이언트를 초기화할 수 없습니다');
            }

            // MCP 연결 진단
            const diagnosis = await this.llmClient.diagnoseMcpConnection();
            console.log('MCP diagnosis result:', diagnosis);

            // 결과 표시
            let message = '';

            switch (diagnosis.status) {
                case 'healthy':
                    message = `✅ MCP 연결이 정상입니다!\n실행 중인 서버: ${diagnosis.runningServers}개`;
                    break;
                case 'no_servers':
                    message = '❌ MCP 서버가 실행되지 않았습니다.\n자동 수정을 시도하시겠습니까?';

                    // 자동 수정 시도
                    if (confirm(message)) {
                        const repairResult = await this.llmClient.repairMcpConnection();
                        if (repairResult.success) {
                            message = '✅ MCP 서버가 성공적으로 시작되었습니다!';
                        } else {
                            message = `❌ MCP 서버 시작 실패: ${repairResult.message}`;
                        }
                    }
                    break;
                case 'partial':
                    message = '⚠️ MCP 서버는 실행 중이지만 일부 기능에 문제가 있습니다.';
                    break;
                case 'tool_error':
                    message = `❌ MCP 도구 호출 오류: ${diagnosis.error}`;
                    break;
                default:
                    message = `❌ MCP 연결 오류: ${diagnosis.message}`;
            }

            // 결과를 채팅에 표시
            if (!this.currentChatId) {
                this.createNewChat();
            }

            const chat = this.chats.get(this.currentChatId);
            if (chat) {
                // 사용자 메시지 추가
                const userMessage = {
                    id: this.generateId(),
                    role: 'user',
                    content: 'MCP 연결 테스트',
                    timestamp: new Date().toISOString()
                };

                // 시스템 응답 추가
                const systemMessage = {
                    id: this.generateId(),
                    role: 'assistant',
                    content: message,
                    timestamp: new Date().toISOString(),
                    isSystem: true
                };

                chat.messages.push(userMessage, systemMessage);
                chat.updatedAt = new Date().toISOString();

                this.saveChats();
                this.renderChatList();
                this.renderMessages();
                this.showChatScreen();
                this.scrollToBottom();

                console.log('MCP test result displayed in chat');
            }

            // 추가 정보가 있으면 콘솔에 출력
            if (diagnosis.details) {
                console.log('MCP diagnosis details:', diagnosis.details);
            }

        } catch (error) {
            console.error('MCP connection test failed:', error);

            // 오류를 채팅에 표시
            if (!this.currentChatId) {
                this.createNewChat();
            }

            const chat = this.chats.get(this.currentChatId);
            if (chat) {
                const userMessage = {
                    id: this.generateId(),
                    role: 'user',
                    content: 'MCP 연결 테스트',
                    timestamp: new Date().toISOString()
                };

                const errorMessage = {
                    id: this.generateId(),
                    role: 'assistant',
                    content: `❌ MCP 연결 테스트 실패: ${error.message}`,
                    timestamp: new Date().toISOString(),
                    isSystem: true
                };

                chat.messages.push(userMessage, errorMessage);
                chat.updatedAt = new Date().toISOString();

                this.saveChats();
                this.renderChatList();
                this.renderMessages();
                this.showChatScreen();
                this.scrollToBottom();

                console.log('MCP test error displayed in chat');
            }
        } finally {
            // 로딩 상태 해제
            console.log('MCP test completed, hiding loading...');
            this.showLoading(false);

            // 진행 중 플래그 해제
            this.mcpTestInProgress = false;

            // 추가 UI 상태 복원
            setTimeout(() => {
                const messageInput = document.getElementById('message-input');
                const sendBtn = document.getElementById('send-btn');
                const mcpTestBtn = document.getElementById('mcp-test-btn');

                if (messageInput) {
                    messageInput.disabled = false;
                    messageInput.focus();
                }

                if (sendBtn) {
                    sendBtn.disabled = false;
                    sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
                }

                if (mcpTestBtn) {
                    mcpTestBtn.disabled = false;
                }

                // 로딩 인디케이터 강제 숨김
                const messageLoadingIndicator = document.getElementById('message-loading-indicator');
                if (messageLoadingIndicator) {
                    messageLoadingIndicator.style.display = 'none';
                }

                // 로딩 애니메이션 강제 중지
                this.stopLoadingAnimation();

                console.log('UI state fully restored after MCP test');
            }, 100);
        }
    }

    // MCP 토글 기능
    async toggleMcpEnabled() {
        console.log('Toggling MCP enabled state...');

        try {
            // 현재 MCP 상태 확인
            const currentState = this.getMcpEnabledState();
            const newState = !currentState;

            console.log(`MCP state changing from ${currentState} to ${newState}`);

            // LLM 클라이언트가 있으면 상태 업데이트
            if (this.llmClient) {
                this.llmClient.setMcpEnabled(newState);
                console.log('Updated LLM client MCP state');
            }

            // 상태 저장
            this.saveMcpEnabledState(newState);

            // UI 업데이트
            this.updateMcpToggleUI(newState);

            // 상태 변경 알림
            const statusMessage = newState ? 'MCP 서버 기능이 활성화되었습니다.' : 'MCP 서버 기능이 비활성화되었습니다.';
            console.log(statusMessage);

            // 토스트 메시지 표시 (있다면)
            this.showToast && this.showToast(statusMessage);

        } catch (error) {
            console.error('Failed to toggle MCP state:', error);
            this.updateMcpToggleUI(false, true); // 오류 상태로 표시
        }
    }

    // MCP 활성화 상태 가져오기
    getMcpEnabledState() {
        try {
            const saved = localStorage.getItem('mcp-enabled');
            if (saved !== null) {
                return JSON.parse(saved);
            }

            // 기본값: LLM 클라이언트의 MCP 상태 또는 true
            return this.llmClient?.mcpEnabled ?? true;
        } catch (error) {
            console.error('Failed to get MCP enabled state:', error);
            return true; // 기본값
        }
    }

    // MCP 활성화 상태 저장
    saveMcpEnabledState(enabled) {
        try {
            localStorage.setItem('mcp-enabled', JSON.stringify(enabled));
            console.log('MCP enabled state saved:', enabled);
        } catch (error) {
            console.error('Failed to save MCP enabled state:', error);
        }
    }

    // MCP 토글 UI 업데이트
    updateMcpToggleUI(enabled, hasError = false, isConnecting = false, statusInfo = null) {
        const toggleBtn = document.getElementById('mcp-toggle-btn');
        const tooltip = document.getElementById('mcp-status-tooltip');

        if (!toggleBtn) {
            console.warn('MCP toggle button not found');
            return;
        }

        // 클래스 초기화
        toggleBtn.classList.remove('active', 'disabled', 'error', 'connecting');

        let tooltipText = '';

        if (isConnecting) {
            toggleBtn.classList.add('connecting');
            toggleBtn.title = 'MCP 서버 연결 중...';
            tooltipText = 'MCP 서버 연결 중...';
        } else if (hasError) {
            toggleBtn.classList.add('error');
            toggleBtn.title = 'MCP 서버 오류 - 클릭하여 다시 시도';
            tooltipText = 'MCP 서버 오류 발생';
        } else if (enabled) {
            toggleBtn.classList.add('active');
            toggleBtn.title = 'MCP 서버 활성화됨 - 클릭하여 비활성화';

            if (statusInfo) {
                const connectedCount = Object.values(statusInfo).filter(s => s.connected).length;
                const totalCount = Object.keys(statusInfo).length;
                const toolsCount = Object.values(statusInfo).reduce((sum, s) => sum + (s.toolsCount || 0), 0);

                tooltipText = `MCP 활성화 (${connectedCount}/${totalCount} 서버, ${toolsCount} 도구)`;
            } else {
                tooltipText = 'MCP 서버 활성화됨';
            }
        } else {
            toggleBtn.classList.add('disabled');
            toggleBtn.title = 'MCP 서버 비활성화됨 - 클릭하여 활성화';
            tooltipText = 'MCP 서버 비활성화됨';
        }

        // 툴팁 업데이트
        if (tooltip) {
            tooltip.textContent = tooltipText;
        }

        console.log('MCP toggle UI updated:', { enabled, hasError, isConnecting, tooltipText });
    }

    // MCP 상태 초기화 (앱 시작 시 호출)
    initializeMcpToggleState() {
        const enabled = this.getMcpEnabledState();
        this.updateMcpToggleUI(enabled);

        // LLM 클라이언트가 있으면 상태 동기화
        if (this.llmClient) {
            this.llmClient.setMcpEnabled(enabled);
        }

        // MCP 상태 모니터링 시작
        this.startMcpStatusMonitoring();

        console.log('MCP toggle state initialized:', enabled);
    }

    // MCP 상태 모니터링 시작
    startMcpStatusMonitoring() {
        // 기존 모니터링이 있으면 중지
        if (this.mcpStatusMonitorInterval) {
            clearInterval(this.mcpStatusMonitorInterval);
        }

        // 5초마다 MCP 상태 확인
        this.mcpStatusMonitorInterval = setInterval(async () => {
            await this.checkAndUpdateMcpStatus();
        }, 5000);

        // 즉시 한 번 실행
        this.checkAndUpdateMcpStatus();
    }

    // MCP 상태 확인 및 UI 업데이트
    async checkAndUpdateMcpStatus() {
        try {
            if (!window.electronAPI) {
                return;
            }

            // MCP 클라이언트 상태 확인
            const clientStatus = await window.electronAPI.mcpClientGetStatus();
            const enabled = this.getMcpEnabledState();

            if (clientStatus.success) {
                const statusInfo = clientStatus.status;
                const hasConnectedServers = Object.values(statusInfo).some(server => server.connected);
                const hasErrors = Object.values(statusInfo).some(server => !server.connected);

                if (enabled && hasConnectedServers) {
                    this.updateMcpToggleUI(true, false, false, statusInfo);
                } else if (enabled && hasErrors) {
                    this.updateMcpToggleUI(true, true, false, statusInfo);
                } else {
                    this.updateMcpToggleUI(enabled, false, false, statusInfo);
                }
            } else {
                // MCP 클라이언트 상태 확인 실패
                if (enabled) {
                    this.updateMcpToggleUI(true, true, false);
                } else {
                    this.updateMcpToggleUI(false, false, false);
                }
            }
        } catch (error) {
            console.error('Failed to check MCP status:', error);
            const enabled = this.getMcpEnabledState();
            this.updateMcpToggleUI(enabled, true, false);
        }
    }

    // MCP 상태 모니터링 중지
    stopMcpStatusMonitoring() {
        if (this.mcpStatusMonitorInterval) {
            clearInterval(this.mcpStatusMonitorInterval);
            this.mcpStatusMonitorInterval = null;
        }
    }
}

// MCP 서버 관련 기능

// MCP 서버 관리 객체
class MCPServerManager {
    constructor() {
        this.servers = new Map();
        this.serverStatus = {};
        this.initEventListeners();
    }

    initEventListeners() {
        // 서버 상태 변경 이벤트 리스너
        if (window.electronAPI && window.electronAPI.onMcpServerStatusChanged) {
            window.electronAPI.onMcpServerStatusChanged((data) => {
                console.log('MCP server status changed:', data);
                this.updateServerStatus(data.name, data.running);
            });
        }
    }

    async loadServers() {
        try {
            const config = await window.electronAPI.getConfig();
            if (config?.mcpServers) {
                this.servers = new Map(Object.entries(config.mcpServers));
                console.log('Loaded MCP servers:', this.servers);
            } else {
                console.log('No MCP servers found in config');
                this.servers = new Map();
            }
        } catch (error) {
            console.error('Failed to load MCP servers:', error);
            this.servers = new Map();
        }
    }

    async checkServerStatus() {
        try {
            if (window.electronAPI && window.electronAPI.getMcpServerStatus) {
                this.serverStatus = await window.electronAPI.getMcpServerStatus();
                console.log('Current server status:', this.serverStatus);
            } else {
                console.log('getMcpServerStatus not available');
                this.serverStatus = {};
            }
        } catch (error) {
            console.error('Failed to check MCP server status:', error);
            this.serverStatus = {};
        }
    }

    updateServerStatus(serverName, isRunning) {
        this.serverStatus[serverName] = { running: isRunning };
        console.log(`Updated server status: ${serverName} = ${isRunning}`);
    }

    async startServer(name) {
        try {
            const serverConfig = this.servers.get(name);
            if (!serverConfig) {
                throw new Error(`서버 ${name}을(를) 찾을 수 없습니다.`);
            }

            console.log('Starting server:', name, serverConfig);

            // 서버 시작 요청
            const result = await window.electronAPI.startMcpServerWithConfig({
                name,
                command: serverConfig.command,
                args: serverConfig.args || [],
                env: serverConfig.env || {}
            });

            if (result.success) {
                console.log(`${name} 서버가 시작되었습니다.`);
                return true;
            } else {
                throw new Error(result.error || '알 수 없는 오류');
            }
        } catch (error) {
            console.error('Failed to start MCP server:', error);
            throw error;
        }
    }

    async stopServer(name) {
        try {
            const result = await window.electronAPI.stopMcpServer(name);

            if (result.success) {
                console.log(`${name} 서버가 중지되었습니다.`);
                return true;
            } else {
                throw new Error(result.error || '알 수 없는 오류');
            }
        } catch (error) {
            console.error('Failed to stop MCP server:', error);
            throw error;
        }
    }

    async startAllServers() {
        const results = [];
        for (const [name] of this.servers) {
            try {
                await this.startServer(name);
                results.push({ name, success: true });
            } catch (error) {
                console.error(`Failed to start server ${name}:`, error);
                results.push({ name, success: false, error: error.message });
            }
        }
        return results;
    }

    async stopAllServers() {
        const results = [];
        for (const name in this.serverStatus) {
            if (this.serverStatus[name]?.running) {
                try {
                    await this.stopServer(name);
                    results.push({ name, success: true });
                } catch (error) {
                    console.error(`Failed to stop server ${name}:`, error);
                    results.push({ name, success: false, error: error.message });
                }
            }
        }
        return results;
    }
}

// 애플리케이션 초기화
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // 메인 앱 초기화
        window.mcpApp = new MCPDesktopApp();

        // MCP 서버 매니저 초기화
        if (window.mcpApp) {
            window.mcpApp.mcpManager = new MCPServerManager();
            await window.mcpApp.mcpManager.loadServers();
            await window.mcpApp.mcpManager.checkServerStatus();
        }

        console.log('Application initialized successfully');
    } catch (error) {
        console.error('Failed to initialize application:', error);
    }
});

// LLMClient가 없는 경우를 위한 임시 클래스 정의
if (!window.LLMClient) {
    console.log('LLMClient not found, creating temporary implementation');
    
    class TempLLMClient {
        constructor(config) {
            this.config = config || {};
            console.log('Temporary LLMClient created with config:', {
                ...this.config,
                apiKey: this.config.apiKey ? '***' : undefined
            });
        }
        
        async chatCompletion(messages, options = {}) {
            console.log('Using temporary LLMClient.chatCompletion');
            console.log('Messages:', messages);
            console.log('Options:', options);
            
            if (this.config.apiType === 'openai') {
                if (!this.config.apiKey) {
                    throw new Error('OpenAI API 키가 설정되지 않았습니다.');
                }
                
                try {
                    const response = await fetch('https://api.openai.com/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.config.apiKey}`
                        },
                        body: JSON.stringify({
                            model: this.config.model || 'gpt-3.5-turbo',
                            messages: messages,
                            temperature: options.temperature || this.config.temperature || 0.7,
                            max_tokens: options.maxTokens || this.config.maxTokens || 2048
                        })
                    });
                    
                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({}));
                        throw new Error(errorData.error?.message || `HTTP 오류: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    return data.choices[0]?.message?.content || '';
                } catch (error) {
                    console.error('OpenAI API 요청 오류:', error);
                    throw error;
                }
            } else {
                throw new Error('지원되지 않는 API 유형입니다.');
            }
        }
    }
    
    window.LLMClient = TempLLMClient;
}

// 안전 모드 감지 및 UI 조정
function setupSafeGraphicsMode() {
    if (window.safeGraphicsMode) {
        console.log('Running in safe graphics mode');
        
        // CSS 클래스 추가
        document.body.classList.add('safe-graphics-mode');
        
        // 애니메이션 비활성화
        document.documentElement.style.setProperty('--transition-speed', '0s');
        
        // 상태 표시
        const statusElement = document.createElement('div');
        statusElement.className = 'safe-mode-indicator';
        statusElement.textContent = '안전 그래픽 모드';
        document.body.appendChild(statusElement);
        
        // 일반 모드로 전환 버튼 추가
        const restartButton = document.createElement('button');
        restartButton.className = 'restart-normal-mode-btn';
        restartButton.textContent = '일반 모드로 재시작';
        restartButton.onclick = async () => {
            try {
                await window.electronAPI.restartInNormalMode();
            } catch (error) {
                console.error('Failed to restart in normal mode:', error);
            }
        };
        document.body.appendChild(restartButton);
    }
}

// 앱 초기화 시 호출
document.addEventListener('DOMContentLoaded', () => {
    setupSafeGraphicsMode();
    // 기존 초기화 코드...
});
