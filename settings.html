<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>설정 - SBT MCP Desktop</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="settings-container">
        <div class="settings-sidebar">
            <ul class="settings-nav">
                <li><button class="settings-nav-btn active" data-section="general">일반</button></li>
                <li><button class="settings-nav-btn" data-section="llm">LLM 설정</button></li>
                <li><button class="settings-nav-btn" data-section="mcp">MCP 서버</button></li>
                <li><button class="settings-nav-btn" data-section="appearance">모양</button></li>
                <li><button class="settings-nav-btn" data-section="advanced">고급</button></li>
            </ul>
        </div>

        <div class="settings-content">
            <div class="settings-section active" id="general">
                <h2>일반 설정</h2>
                <div class="settings-group">
                    <h3>언어 설정</h3>
                    <div class="form-group">
                        <label for="language">언어</label>
                        <select id="language">
                            <option value="ko">한국어</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>시작 설정</h3>
                    <div class="checkbox-group">
                        <label for="startAtLogin">
                            <input type="checkbox" id="startAtLogin">
                            <span data-i18n="startAtLogin">시스템 시작 시 자동 실행</span>
                        </label>
                    </div>
                    <div class="checkbox-group">
                        <label for="minimizeToTray">
                            <input type="checkbox" id="minimizeToTray">
                            <span data-i18n="minimizeToTray">시스템 트레이로 최소화</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="settings-section" id="llm">
                <h2>LLM 설정</h2>

                <div class="settings-group">
                    <h3>LLM 모델 설정</h3>
                    <div class="form-group">
                        <label for="llmModelSelect">LLM 모델</label>
                        <select id="llmModelSelect" style="width: 100%;">
                            <option value="anthropic">Anthropic (Claude)</option>
                            <option value="openai">OpenAI (GPT)</option>
                            <option value="ollama">Ollama (로컬)</option>
                            <option value="llamacpp">LLaMA.cpp</option>
                            <option value="custom">사용자 정의</option>
                        </select>
                        <small style="color: var(--text-secondary); margin-top: 5px; display: block;">
                            사용할 LLM 모델을 선택하세요. 선택하면 관련 설정이 자동으로 업데이트됩니다.
                        </small>
                    </div>

                    <div class="form-group api-url-group">
                        <label for="apiUrl">API URL</label>
                        <input type="url" id="apiUrl" placeholder="API 서버 URL">
                        <small class="api-url-hint" style="color: var(--text-secondary); margin-top: 5px; display: block;">
                            API 서버의 기본 URL을 입력하세요.
                        </small>
                    </div>

                    <div class="form-group" id="customApiUrlContainer" style="display:none;">
                        <label for="customApiUrl">사용자 정의 API URL</label>
                        <input type="url" id="customApiUrl" placeholder="https://your-custom-api.com/v1/chat/completions">
                    </div>

                    <div class="form-group" id="apiKeyContainer">
                        <label for="apiKey">API 키</label>
                        <div class="input-with-button">
                            <input type="password" id="apiKey" placeholder="API 키를 입력하세요">
                            <button id="toggleApiKey" title="API 키 표시/숨기기"><i class="fas fa-eye"></i></button>
                        </div>
                        <small class="api-key-hint" style="color: var(--text-secondary); margin-top: 5px; display: block;">
                            API 키는 암호화되어 저장됩니다.
                        </small>
                    </div>
                </div>

                <div class="settings-group">
                    <!-- 숨겨진 API 타입 필드 (내부적으로 사용) -->
                    <input type="hidden" id="apiType" value="anthropic">

                    <div class="form-group">
                        <label for="modelSelect">세부 모델 선택</label>
                        <select id="modelSelect" style="width: 100%;">
                            <option value="">적용 모델을 선택하세요</option>
                            <!-- 모델 옵션들이 JavaScript에서 동적으로 추가됩니다 -->
                        </select>
                        <small style="color: var(--text-secondary); margin-top: 5px; display: block;">
                            선택한 LLM 모델에 해당하는 세부 모델을 선택하세요.
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="modelName">모델 이름 (직접 입력)</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <input type="text" id="modelName" placeholder="모델명을 직접 입력하세요" style="flex: 1;">
                            <button type="button" class="btn btn-secondary btn-sm" id="refreshModels" title="모델 목록 새로고침">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <small class="model-hint" style="color: var(--text-secondary); margin-top: 5px; display: block;">
                            모델명을 직접 입력하거나 새로고침 버튼으로 사용 가능한 모델을 확인하세요.
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="temperature">Temperature</label>
                        <input type="range" id="temperature" min="0" max="2" step="0.1" value="0.7">
                        <span id="temperatureValue">0.7</span>
                    </div>
                    <div class="form-group">
                        <label for="maxTokens">Max Tokens</label>
                        <input type="number" id="maxTokens" value="2048" min="1" max="8192">
                    </div>
                </div>

                <div class="settings-group">
                    <h3>프롬프트 설정</h3>

                    <div class="form-group">
                        <label for="systemPrompt">시스템 프롬프트</label>
                        <textarea id="systemPrompt" rows="4" placeholder="시스템 역할과 행동 방식을 정의하는 프롬프트를 입력하세요...">당신은 도움이 되고 정확한 AI 어시스턴트입니다. 사용자의 질문에 친절하고 상세하게 답변해주세요.</textarea>
                        <small style="color: var(--text-secondary); margin-top: 5px; display: block;">
                            모든 대화의 시작에 적용되는 시스템 메시지입니다.
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="userPromptTemplate">사용자 프롬프트 템플릿</label>
                        <textarea id="userPromptTemplate" rows="3" placeholder="사용자 입력을 감싸는 템플릿을 입력하세요...">{user_input}</textarea>
                        <small style="color: var(--text-secondary); margin-top: 5px; display: block;">
                            {user_input}은 사용자가 입력한 내용으로 대체됩니다. 예: "질문: {user_input}\n답변:"
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="conversationStarters">대화 시작 문구</label>
                        <textarea id="conversationStarters" rows="3" placeholder="대화 시작 시 표시할 문구들을 한 줄씩 입력하세요...">안녕하세요! 무엇을 도와드릴까요?
오늘 어떤 것에 대해 이야기하고 싶으신가요?
궁금한 것이 있으시면 언제든 물어보세요!</textarea>
                        <small style="color: var(--text-secondary); margin-top: 5px; display: block;">
                            새 대화 시작 시 랜덤으로 표시될 문구들입니다. 한 줄에 하나씩 입력하세요.
                        </small>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="enableSystemPrompt" checked>
                            시스템 프롬프트 사용
                        </label>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="enablePromptTemplate">
                            사용자 프롬프트 템플릿 사용
                        </label>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>연결 테스트</h3>
                    <button class="btn btn-primary" id="testConnection">연결 테스트</button>
                    <div id="connectionStatus" style="margin-top: 10px;"></div>
                </div>
            </div>

            <div class="settings-section" id="mcp">
                <h2>MCP 서버 관리</h2>
                
                <div class="settings-group">
                    <h3>등록된 서버</h3>
                    <div class="mcp-server-list" id="mcpServersList">
                        <!-- 서버 목록이 여기에 동적으로 추가됩니다 -->
                        <div class="empty-state">등록된 MCP 서버가 없습니다.</div>
                    </div>
                    <button class="btn btn-primary" id="addServerBtn">+ 서버 추가</button>
                    
                    <div class="add-server-form" id="addServerForm">
                        <h4>MCP 서버 추가/수정</h4>
                        <div class="form-group">
                            <label for="serverName">서버 이름</label>
                            <input type="text" id="serverName" placeholder="예: filesystem">
                        </div>
                        <div class="form-group">
                            <label for="serverCommand">실행 명령</label>
                            <input type="text" id="serverCommand" placeholder="예: npx -y @modelcontextprotocol/server-filesystem" style="width: 100%;">
                            <small class="form-hint">전체 명령어를 한 줄로 입력하세요. 예: npx -y @modelcontextprotocol/server-filesystem</small>
                        </div>
                        
                        <div class="form-group">
                            <label>허용 디렉토리</label>
                            <div id="allowedDirectoriesList" class="allowed-directories-list">
                                <!-- 허용 디렉토리 목록이 여기에 동적으로 추가됩니다 -->
                            </div>
                            <div style="display: flex; gap: 10px; margin-top: 10px;">
                                <input type="text" id="newDirectoryPath" placeholder="디렉토리 경로를 입력하세요" style="flex: 1;">
                                <button type="button" class="btn btn-secondary" id="addDirectoryBtn">추가</button>
                                <button type="button" class="btn btn-secondary" id="browseDirectoryBtn">찾아보기</button>
                            </div>
                            <small class="form-hint">MCP 서버가 접근할 수 있는 디렉토리를 추가하세요. 사용자가 선택한 폴더만 사용됩니다.</small>
                        </div>

                        <div class="form-group">
                            <label for="serverEnv">환경 변수 (JSON)</label>
                            <textarea id="serverEnv" placeholder='{}'></textarea>
                            <small class="form-hint">환경 변수를 JSON 형식으로 입력하세요. 예: {"PORT": "3000", "DEBUG": "true"}</small>
                        </div>
                        <div style="display: flex; gap: 10px; margin-top: 15px;">
                            <button class="btn btn-primary" id="saveServerBtn">저장</button>
                            <button class="btn btn-secondary" id="cancelServerBtn">취소</button>
                        </div>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>MCP 서버 상태</h3>
                    <div class="server-status-container">
                        <div id="mcpServerStatus">
                            <!-- 서버 상태가 여기에 표시됩니다 -->
                            <div class="empty-state">실행 중인 MCP 서버가 없습니다.</div>
                        </div>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>MCP 클라이언트 상태</h3>
                    <div class="client-status-container">
                        <div id="mcpClientStatus">
                            <!-- 클라이언트 상태가 여기에 표시됩니다 -->
                            <div class="empty-state">연결된 MCP 클라이언트가 없습니다.</div>
                        </div>
                        <div style="display: flex; gap: 10px; margin-top: 10px;">
                            <button class="btn btn-secondary" id="refreshClientStatus">클라이언트 상태 새로고침</button>
                            <button class="btn btn-primary" id="diagnoseMcp">🔍 MCP 진단</button>
                        </div>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>사용 가능한 도구</h3>
                    <div class="tools-container">
                        <div id="mcpToolsList">
                            <!-- 사용 가능한 도구 목록이 여기에 표시됩니다 -->
                            <div class="empty-state">사용 가능한 도구가 없습니다.</div>
                        </div>
                        <button class="btn btn-secondary" id="refreshTools">도구 목록 새로고침</button>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Claude Desktop 호환</h3>
                    <div class="claude-compat-container">
                        <button class="btn btn-secondary" id="exportClaudeConfig">Claude Desktop 설정 내보내기</button>
                        <button class="btn btn-secondary" id="importClaudeConfig">Claude Desktop 설정 가져오기</button>
                        <small class="form-hint">Claude Desktop의 mcp_desktop_config.json 파일과 호환되는 형식으로 설정을 가져오거나 내보낼 수 있습니다. Claude Desktop 설정을 가져오면 기존 설정이 덮어쓰여질 수 있습니다.</small>
                    </div>
                </div>
            </div>

            <div class="settings-section" id="appearance">
                <h2>모양 설정</h2>

                <div class="settings-group">
                    <h3>테마</h3>
                    <div class="form-group">
                        <label for="theme">테마</label>
                        <select id="theme">
                            <option value="dark">다크 모드</option>
                            <option value="light">라이트 모드</option>
                            <option value="auto">시스템 설정 따라가기</option>
                        </select>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>폰트</h3>
                    <div class="form-group">
                        <label for="fontSize">폰트 크기</label>
                        <select id="fontSize">
                            <option value="10">10px</option>
                            <option value="11">11px</option>
                            <option value="12">12px</option>
                            <option value="13">13px</option>
                            <option value="14">14px</option>
                            <option value="15">15px</option>
                            <option value="16">16px</option>
                            <option value="17">17px</option>
                            <option value="18">18px</option>
                            <option value="19">19px</option>
                            <option value="20">20px</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="fontFamily">폰트 패밀리</label>
                        <select id="fontFamily">
                            <option value="system">시스템 기본</option>
                            <option value="monospace">모노스페이스</option>
                            <option value="serif">세리프</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="settings-section" id="advanced">
                <h2>고급 설정</h2>

                <div class="settings-group">
                    <h3>개발자 도구</h3>
                    <div class="checkbox-group">
                        <label for="enableDevTools">
                            <input type="checkbox" id="enableDevTools">
                            <span data-i18n="enableDevTools">개발자 도구 활성화</span>
                        </label>
                    </div>
                    <div class="checkbox-group">
                        <label for="enableLogging">
                            <input type="checkbox" id="enableLogging">
                            <span data-i18n="enableLogging">상세 로깅 활성화</span>
                        </label>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>데이터 관리</h3>
                    <button class="btn btn-secondary" id="exportSettings">설정 내보내기</button>
                    <button class="btn btn-secondary" id="importSettings">설정 가져오기</button>
                    <button class="btn btn-primary" id="resetSettings">설정 초기화</button>
                </div>
            </div>
        </div>

        <div class="settings-footer">
            <button class="btn btn-secondary" id="cancelBtn">취소</button>
            <button class="btn btn-primary" id="saveBtn">저장</button>
        </div>
    </div>

    <script src="llm-client.js"></script>
    <script src="settings.js"></script>
    <script>
        // iframe에서 부모 창과 통신하기 위한 스크립트
        document.addEventListener('DOMContentLoaded', function() {
            // 설정 저장 시 부모 창에 알림
            const saveBtn = document.getElementById('saveBtn');
            const cancelBtn = document.getElementById('cancelBtn');

            if (saveBtn) {
                saveBtn.addEventListener('click', function() {
                    // 부모 창에 설정 저장 완료 알림
                    if (window.parent && window.parent !== window) {
                        window.parent.postMessage({ type: 'settings-saved' }, '*');
                    }
                });
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', function() {
                    // 부모 창에 설정 취소 알림
                    if (window.parent && window.parent !== window) {
                        window.parent.postMessage({ type: 'settings-cancelled' }, '*');
                    }
                });
            }
        });
    </script>
</body>
</html>


