const { contextBridge, ipcRenderer } = require('electron');

// 디버깅을 위한 로그 추가
console.log('Preload script executing...');

// 플랫폼 정보 노출
const platform = {
    isMac: process.platform === 'darwin',
    isWindows: process.platform === 'win32',
    isLinux: process.platform === 'linux'
};

// API 노출
contextBridge.exposeInMainWorld('platform', platform);

// 메인 프로세스와 통신할 API 노출
contextBridge.exposeInMainWorld('electronAPI', {
    // 윈도우 컨트롤 관련 API
    minimizeWindow: () => {
        console.log('Calling minimizeWindow via IPC');
        return ipcRenderer.invoke('window-minimize');
    },
    maximizeWindow: () => {
        console.log('Calling maximizeWindow via IPC');
        return ipcRenderer.invoke('window-maximize');
    },
    closeWindow: () => {
        console.log('Calling closeWindow via IPC');
        return ipcRenderer.invoke('window-close');
    },
    
    // 윈도우 상태 이벤트
    onWindowMaximized: (callback) => ipcRenderer.on('window-maximized', () => callback()),
    onWindowUnmaximized: (callback) => ipcRenderer.on('window-unmaximized', () => callback()),

    // LLM 설정 변경 이벤트
    onLlmConfigChanged: (callback) => ipcRenderer.on('llm-config-changed', (event, config) => callback(config)),
    
    // 설정 관련
    getConfig: () => ipcRenderer.invoke('get-config'),
    saveConfig: (config) => {
        console.log('Calling saveConfig via IPC with config:', config);
        return ipcRenderer.invoke('save-config', config);
    },
    saveMcpConfig: (mcpConfig) => {
        console.log('Calling saveMcpConfig via IPC with config:', mcpConfig);
        return ipcRenderer.invoke('save-mcp-config', mcpConfig);
    },
    closeSettings: () => {
        console.log('Calling closeSettings via IPC');
        return ipcRenderer.invoke('close-settings');
    },
    resetConfig: () => ipcRenderer.invoke('reset-config'),

    // API 키 관련
    saveApiKey: (apiKey) => ipcRenderer.invoke('save-api-key', apiKey),
    getApiKey: () => ipcRenderer.invoke('get-api-key'),
    // 디버깅용 직접 API 키 관련
    saveApiKeyDirect: (apiKey) => ipcRenderer.invoke('save-api-key-direct', apiKey),
    getApiKeyDirect: () => ipcRenderer.invoke('get-api-key-direct'),

    // LLM 설정 관련
    getLlmConfig: () => ipcRenderer.invoke('get-llm-config'),
    saveLlmConfig: (config) => ipcRenderer.invoke('save-llm-config', config),
    notifyLlmConfigChanged: (config) => ipcRenderer.invoke('notify-llm-config-changed', config),

    // 언어 변경
    changeLanguage: (language) => ipcRenderer.invoke('change-language', language),

    // MCP 서버 관련
    startMcpServerWithConfig: (config) => ipcRenderer.invoke('mcp:start-server-with-config', config),
    stopMcpServer: (name) => ipcRenderer.invoke('mcp:stop-server', name),
    getMcpServerStatus: () => ipcRenderer.invoke('mcp:get-server-status'),
    mcpFileAccess: (params) => ipcRenderer.invoke('mcp:file-access', params),
    testMcpConnection: () => ipcRenderer.invoke('mcp:test-connection'),
    addMcpServer: (name, config) => ipcRenderer.invoke('mcp:add-server', name, config),
    removeMcpServer: (name) => ipcRenderer.invoke('mcp:remove-server', name),
    getMcpServers: () => ipcRenderer.invoke('mcp:get-servers'),

    // MCP 클라이언트 관련
    mcpClientConnectServer: (name, config) => ipcRenderer.invoke('mcp:client:connect-server', name, config),
    mcpClientDisconnectServer: (name) => ipcRenderer.invoke('mcp:client:disconnect-server', name),
    mcpClientGetTools: () => ipcRenderer.invoke('mcp:client:get-tools'),
    mcpClientCallTool: (toolName, args) => ipcRenderer.invoke('mcp:client:call-tool', toolName, args),
    mcpClientGetStatus: () => ipcRenderer.invoke('mcp:client:get-status'),
    mcpDiagnose: () => ipcRenderer.invoke('mcp:diagnose'),
    // LLM이 MCP 도구를 사용할 수 있도록 개선된 함수
    llmUseMcpTools: (params) => {
        console.log('Calling llmUseMcpTools via IPC with params:', params);
        return ipcRenderer.invoke('llm:use-mcp-tools', params)
            .then(result => {
                console.log('llmUseMcpTools result:', result);
                return result;
            })
            .catch(error => {
                console.error('llmUseMcpTools error:', error);
                // 오류 발생 시 mcpFileAccess로 대체 시도
                if (params.tool === 'read_file') {
                    console.log('Falling back to mcpFileAccess for read_file');
                    return ipcRenderer.invoke('mcp:file-access', {
                        operation: 'read',
                        path: params.args.path
                    });
                } else if (params.tool === 'write_file') {
                    console.log('Falling back to mcpFileAccess for write_file');
                    return ipcRenderer.invoke('mcp:file-access', {
                        operation: 'write',
                        path: params.args.path,
                        content: params.args.content
                    });
                } else if (params.tool === 'list_files') {
                    console.log('Falling back to mcpFileAccess for list_files');
                    return ipcRenderer.invoke('mcp:file-access', {
                        operation: 'list',
                        path: params.args.path
                    });
                }
                throw error;
            });
    },

    // 파일 시스템
    showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
    showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
    writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
    readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),

    // API 요청 (메인 프로세스를 통한 안전한 API 호출)
    apiRequest: (requestData) => ipcRenderer.invoke('api-request', requestData),

    // 이벤트 리스너
    onMcpServerStatusChanged: (callback) => ipcRenderer.on('mcp:server-status-changed', (_, data) => callback(data)),
    onLanguageChanged: (callback) => ipcRenderer.on('language-changed', (_, language) => callback(language)),
    onNewChat: (callback) => ipcRenderer.on('new-chat', callback),
    onExportChat: (callback) => ipcRenderer.on('export-chat', callback),
    onOpenSettings: (callback) => ipcRenderer.on('open-settings', callback),
    onToggleSidebar: (callback) => ipcRenderer.on('toggle-sidebar', callback),
    onCloseSettings: (callback) => ipcRenderer.on('close-settings', callback),

    // 이벤트 리스너 제거
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),

    // 모델명 업데이트 메서드
    updateModelName: (modelName) => ipcRenderer.invoke('update-model-name', modelName),

    // 모델명 변경 이벤트 리스너
    onModelChanged: (callback) => ipcRenderer.on('model-changed', (_, modelName) => callback(modelName)),

    // 설정 패널 관련 메서드 추가
    hideSettingsPanel: () => ipcRenderer.invoke('hide-settings-panel'),
    settingsSaved: () => ipcRenderer.invoke('settings-saved'),

    // 설정 패널 관련 이벤트 리스너
    onHideSettingsPanel: (callback) => ipcRenderer.on('hide-settings-panel', () => callback()),
    onSettingsSaved: (callback) => ipcRenderer.on('settings-saved', () => callback()),

    // 안전 모드 관련
    isSafeGraphicsMode: () => process.argv.includes('--safe-graphics-mode'),
    restartInNormalMode: () => ipcRenderer.invoke('restart-in-normal-mode'),
    restartInSafeMode: () => ipcRenderer.invoke('restart-in-safe-mode'),

    // 창 닫기 전 이벤트
    onBeforeClose: (callback) => {
        ipcRenderer.on('before-close', () => callback());
    }
});

console.log('Preload script completed, exposed electronAPI:', Object.keys(contextBridge.exposeInMainWorld).join(', '));

// 개발 모드 체크
contextBridge.exposeInMainWorld('isDev', process.env.NODE_ENV === 'development');

// 안전한 console 로깅
contextBridge.exposeInMainWorld('safeConsole', {
    log: (...args) => console.log(...args),
    error: (...args) => console.error(...args),
    warn: (...args) => console.warn(...args),
    info: (...args) => console.info(...args)
});

// 안전 모드 상태 노출
contextBridge.exposeInMainWorld('safeGraphicsMode', process.argv.includes('--safe-graphics-mode'));

// LLMClient 클래스 로드 및 전역 객체로 노출
try {
    // 필요한 모듈 가져오기
    const path = require('path');
    const fs = require('fs');
    
    // 절대 경로로 파일 로드 시도
    const appDir = path.dirname(require.main.filename);
    const llmClientPath = path.join(appDir, 'llm-client.js');
    
    console.log('Attempting to load LLMClient from:', llmClientPath);
    
    if (fs.existsSync(llmClientPath)) {
        const LLMClient = require(llmClientPath);
        contextBridge.exposeInMainWorld('LLMClient', LLMClient);
        console.log('LLMClient class exposed to renderer process successfully');
    } else {
        console.error('LLMClient file not found at path:', llmClientPath);
        // 상대 경로로 시도
        const LLMClient = require('./llm-client.js');
        contextBridge.exposeInMainWorld('LLMClient', LLMClient);
        console.log('LLMClient class exposed to renderer process using relative path');
    }
} catch (error) {
    console.error('Failed to load LLMClient class:', error);
}
