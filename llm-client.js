// EventEmitter 클래스 정의 (브라우저 환경용)
class EventEmitter {
    constructor() {
        this.events = {};
    }

    on(event, listener) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(listener);
        return this;
    }

    emit(event, ...args) {
        if (this.events[event]) {
            this.events[event].forEach(listener => listener(...args));
        }
        return this;
    }

    removeListener(event, listener) {
        if (this.events[event]) {
            this.events[event] = this.events[event].filter(l => l !== listener);
        }
        return this;
    }
}

// LLM 클라이언트 클래스
class LLMClient extends EventEmitter {
    constructor(config) {
        super();

        // 기본 설정
        this.config = {
            apiType: 'openai',
            apiUrl: 'https://api.openai.com',
            model: 'gpt-3.5-turbo',
            temperature: 0.7,
            maxTokens: 2048,
            ...config
        };

        // API 타입 정규화 (소문자로 변환)
        this.config.apiType = (this.config.apiType || 'openai').toLowerCase();

        // MCP 클라이언트 매니저 초기화
        this.mcpManager = null;
        this.mcpEnabled = true;

        console.log('LLM client initialized with config:', {
            apiType: this.config.apiType,
            apiUrl: this.config.apiUrl,
            model: this.config.model,
            temperature: this.config.temperature,
            maxTokens: this.config.maxTokens,
            hasApiKey: !!this.config.apiKey
        });
    }

    // MCP 매니저 설정
    setMcpManager(mcpManager) {
        this.mcpManager = mcpManager;
        console.log('MCP manager set for LLM client');

        // MCP 매니저가 설정되면 자동으로 활성화
        if (mcpManager) {
            this.mcpEnabled = true;
            console.log('MCP integration automatically enabled');
        }
    }

    // MCP 활성화/비활성화
    setMcpEnabled(enabled) {
        this.mcpEnabled = enabled;
        console.log(`MCP integration ${enabled ? 'enabled' : 'disabled'}`);
    }

    // 사용 가능한 MCP 도구 가져오기
    async getAvailableMcpTools() {
        if (!this.mcpManager || !this.mcpEnabled) {
            console.log('MCP manager not available or disabled');
            return [];
        }

        try {
            const tools = await this.mcpManager.getAllAvailableTools();
            console.log('Retrieved MCP tools:', tools);
            return tools || [];
        } catch (error) {
            console.error('Failed to get MCP tools:', error);
            return [];
        }
    }

    // MCP 도구 호출
    async callMcpTool(toolName, args = {}) {
        if (!this.mcpManager || !this.mcpEnabled) {
            throw new Error('MCP is not available or disabled');
        }

        try {
            console.log(`Calling MCP tool: ${toolName}`, args);

            // electronAPI를 통해 메인 프로세스의 MCP 클라이언트 호출
            if (window.electronAPI && window.electronAPI.mcpClientCallTool) {
                console.log('Using electronAPI.mcpClientCallTool');
                const result = await window.electronAPI.mcpClientCallTool(toolName, args);
                if (result.success) {
                    console.log(`MCP tool ${toolName} result:`, result.result);
                    return result.result;
                } else {
                    throw new Error(result.error || 'MCP tool call failed');
                }
            } else {
                // 폴백: MCP 매니저 직접 호출
                console.log('Using mcpManager.callTool (fallback)');
                const result = await this.mcpManager.callTool(toolName, args);
                console.log(`MCP tool ${toolName} result:`, result);
                return result;
            }
        } catch (error) {
            console.error(`Failed to call MCP tool ${toolName}:`, error);
            throw error;
        }
    }

    // 설정 업데이트
    updateConfig(newConfig) {
        if (!newConfig) return;
        
        console.log('Updating LLM client config:', {
            ...newConfig,
            apiKey: newConfig.apiKey ? '***' : undefined
        });
        
        this.config = {
            ...this.config,
            ...newConfig
        };
        
        if (this.config.apiType) {
            this.config.apiType = this.config.apiType.toLowerCase();
        }
        
        // 로컬 스토리지에서 API 키 확인
        if ((this.config.apiType === 'openai' || this.config.apiType === 'anthropic' || this.config.apiType === 'custom') 
            && !this.config.apiKey) {
            const storageKey = this.config.apiType === 'openai' ? 'openai-api-key' : 
                             this.config.apiType === 'anthropic' ? 'anthropic-api-key' : 
                             'custom-api-key';
            const localApiKey = localStorage.getItem(storageKey);
            if (localApiKey) {
                this.config.apiKey = localApiKey;
                console.log(`Using API key from localStorage (${storageKey})`);
            }
        }
    }

    // API 연결 테스트
    async testConnection() {
        try {
            console.log('Testing connection to API type:', this.config.apiType);
            
            switch (this.config.apiType) {
                case 'openai':
                    if (!this.config.apiKey) {
                        throw new Error('OpenAI API 키가 설정되지 않았습니다.');
                    }

                    const response = await fetch('https://api.openai.com/v1/models', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${this.config.apiKey}`,
                            'Accept': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        return {
                            success: true,
                            message: `OpenAI API 연결 성공! (사용 가능한 모델: ${data.data ? data.data.length : 0}개)`,
                            info: { models: data.data ? data.data.length : 0 }
                        };
                    } else {
                        const errorText = await response.text();
                        throw new Error(`HTTP ${response.status}: ${errorText}`);
                    }
                    
                case 'anthropic':
                    if (!this.config.apiKey) {
                        throw new Error('Anthropic API 키가 설정되지 않았습니다.');
                    }
                    
                    return {
                        success: true,
                        message: 'Anthropic API 키가 설정되어 있습니다.',
                        info: { endpoint: 'Anthropic API' }
                    };
                    
                case 'ollama':
                case 'local':
                    if (!this.config.apiUrl) {
                        throw new Error('Ollama API URL이 설정되지 않았습니다.');
                    }
                    
                    const ollamaResponse = await fetch(`${this.config.apiUrl}/api/tags`, {
                        method: 'GET',
                        headers: { 'Accept': 'application/json' }
                    });
                    
                    if (ollamaResponse.ok) {
                        const data = await ollamaResponse.json();
                        return {
                            success: true,
                            message: `Ollama API 연결 성공! (사용 가능한 모델: ${data.models ? data.models.length : 0}개)`,
                            info: { models: data.models ? data.models.length : 0 }
                        };
                    } else {
                        throw new Error(`Ollama 서버 연결 실패: HTTP ${ollamaResponse.status}`);
                    }
                    
                case 'llamacpp':
                    if (!this.config.apiUrl) {
                        throw new Error('LLaMA.cpp API URL이 설정되지 않았습니다.');
                    }
                    
                    return {
                        success: true,
                        message: 'LLaMA.cpp 서버 설정이 완료되었습니다.',
                        info: { endpoint: this.config.apiUrl }
                    };
                    
                case 'custom':
                    if (!this.config.apiUrl) {
                        throw new Error('사용자 정의 API URL이 설정되지 않았습니다.');
                    }
                    
                    return {
                        success: true,
                        message: '사용자 정의 API 설정이 완료되었습니다.',
                        info: { endpoint: this.config.apiUrl }
                    };
                    
                default:
                    throw new Error(`지원되지 않는 API 유형: ${this.config.apiType}`);
            }
        } catch (error) {
            console.error('Connection test error:', error);
            return { success: false, error: error.message };
        }
    }

    // MCP와 통합된 메시지 처리
    async sendMessage(messages, options = {}) {
        console.log('sendMessage called with MCP status:', {
            mcpEnabled: this.mcpEnabled,
            hasMcpManager: !!this.mcpManager,
            messagesCount: messages.length
        });

        // MCP가 활성화되어 있으면 MCP 통합 처리 사용
        if (this.mcpEnabled && this.mcpManager) {
            console.log('Using MCP-integrated message processing');
            return await this.processMessageWithMcp(messages, options);
        }

        // 일반 채팅 완성 사용
        console.log('Using standard chat completion (no MCP)');
        return await this.chatCompletion(messages, options);
    }

    // 메시지에서 도구 호출 감지 및 처리
    async processMessageWithMcp(messages, options = {}) {
        try {
            console.log('Processing message with MCP integration...');

            // 사용 가능한 도구 목록 가져오기
            const availableTools = await this.getAvailableMcpTools();
            console.log(`Found ${availableTools.length} available MCP tools:`, availableTools.map(t => t.name));

            if (availableTools.length === 0) {
                console.log('No MCP tools available, proceeding without MCP');
                return await this.chatCompletion(messages, options);
            }

            // API 타입에 따른 도구 처리
            const apiType = this.config.apiType?.toLowerCase();

            if (apiType === 'anthropic' || apiType === 'openai') {
                // 네이티브 도구 지원 API
                console.log(`Using native tool support for ${apiType}`);
                const formattedTools = this.formatToolsForLLM(availableTools);
                const enhancedOptions = { ...options, tools: formattedTools };

                return await this.chatCompletion(messages, enhancedOptions);
            } else {
                // 텍스트 기반 도구 처리
                console.log('Using text-based tool processing');
                const toolsInfo = this.formatToolsForGeneric(availableTools);
                const enhancedMessages = this.addToolsToMessages(messages, toolsInfo);
                console.log('Enhanced messages with tool info:', enhancedMessages.length, 'messages');

                // LLM에 메시지 전송
                console.log('Sending enhanced messages to LLM...');
                const response = await this.chatCompletion(enhancedMessages, options);
                console.log('LLM response received, checking for tool calls...');

                // 응답에서 도구 호출 감지 및 실행
                const processedResponse = await this.processToolCalls(response);
                console.log('Tool call processing completed');

                return processedResponse;
            }
        } catch (error) {
            console.error('Error processing message with MCP:', error);
            // MCP 오류 시 일반 메시지로 폴백
            console.log('Falling back to standard chat completion');
            return await this.chatCompletion(messages, options);
        }
    }

    // 도구 정보를 LLM이 이해할 수 있는 형식으로 변환
    formatToolsForLLM(tools) {
        if (this.config.apiType === 'anthropic') {
            return this.formatToolsForAnthropic(tools);
        } else if (this.config.apiType === 'openai') {
            return this.formatToolsForOpenAI(tools);
        } else {
            return this.formatToolsForGeneric(tools);
        }
    }

    // Anthropic용 도구 형식
    formatToolsForAnthropic(tools) {
        return tools.map(tool => ({
            name: tool.name,
            description: tool.description || `MCP tool: ${tool.name}`,
            input_schema: tool.inputSchema || {
                type: "object",
                properties: {},
                required: []
            }
        }));
    }

    // OpenAI용 도구 형식
    formatToolsForOpenAI(tools) {
        return tools.map(tool => ({
            type: "function",
            function: {
                name: tool.name,
                description: tool.description || `MCP tool: ${tool.name}`,
                parameters: tool.inputSchema || {
                    type: "object",
                    properties: {},
                    required: []
                }
            }
        }));
    }

    // 일반적인 텍스트 기반 도구 형식
    formatToolsForGeneric(tools) {
        const toolDescriptions = tools.map(tool => {
            const params = tool.inputSchema?.properties ?
                Object.keys(tool.inputSchema.properties).join(', ') : 'none';

            return `- ${tool.name}: ${tool.description || 'No description'} (Parameters: ${params})`;
        }).join('\n');

        return `Available tools:\n${toolDescriptions}\n\nTo use a tool, respond with: TOOL_CALL: tool_name(param1=value1, param2=value2)`;
    }

    // 메시지에 도구 정보 추가
    addToolsToMessages(messages, toolsInfo) {
        const toolsSystemContent = `You have access to the following tools. Use them when appropriate to help answer user questions.\n\n${toolsInfo}`;

        // 기존 시스템 메시지가 있으면 병합, 없으면 추가
        const enhancedMessages = [...messages];
        const existingSystemIndex = enhancedMessages.findIndex(msg => msg.role === 'system');

        if (existingSystemIndex >= 0) {
            // 기존 시스템 메시지에 도구 정보 추가
            const existingContent = enhancedMessages[existingSystemIndex].content;
            enhancedMessages[existingSystemIndex].content = `${existingContent}\n\n${toolsSystemContent}`;
            console.log('Merged tools info with existing system message');
        } else {
            // 새로운 시스템 메시지로 도구 정보 추가
            enhancedMessages.unshift({
                role: 'system',
                content: toolsSystemContent
            });
            console.log('Added new system message with tools info');
        }

        return enhancedMessages;
    }

    // 응답에서 도구 호출 감지 및 실행
    async processToolCalls(response) {
        const toolCallRegex = /TOOL_CALL:\s*(\w+)\((.*?)\)/g;
        let match;
        let processedResponse = response;
        let toolCallsFound = 0;

        console.log('Scanning response for tool calls...');
        console.log('Response preview:', response.substring(0, 200) + '...');

        while ((match = toolCallRegex.exec(response)) !== null) {
            toolCallsFound++;
            const toolName = match[1];
            const argsString = match[2];

            console.log(`Found tool call #${toolCallsFound}: ${toolName}(${argsString})`);

            try {
                // 인수 파싱
                const args = this.parseToolArguments(argsString);
                console.log('Parsed arguments:', args);

                // 도구 호출
                console.log(`Calling MCP tool: ${toolName}`);
                const toolResult = await this.callMcpTool(toolName, args);
                console.log(`Tool ${toolName} result:`, toolResult);

                // 결과를 응답에 삽입
                const toolCallText = match[0];
                const resultText = `\n\n[Tool Result: ${toolName}]\n${JSON.stringify(toolResult, null, 2)}\n`;

                processedResponse = processedResponse.replace(toolCallText, toolCallText + resultText);
                console.log(`Tool result inserted for ${toolName}`);

            } catch (error) {
                console.error(`Failed to execute tool call ${toolName}:`, error);
                const errorText = `\n\n[Tool Error: ${toolName}]\n${error.message}\n`;
                processedResponse = processedResponse.replace(match[0], match[0] + errorText);
            }
        }

        if (toolCallsFound === 0) {
            console.log('No tool calls found in response');
        }

        console.log(`🏁 Tool call processing completed. Found ${toolCallsFound} tool calls.`);
        return processedResponse;
    }

    // 도구 인수 파싱
    parseToolArguments(argsString) {
        const args = {};
        if (!argsString.trim()) {
            return args;
        }

        // 간단한 key=value 파싱
        const pairs = argsString.split(',').map(s => s.trim());
        for (const pair of pairs) {
            const [key, ...valueParts] = pair.split('=');
            if (key && valueParts.length > 0) {
                let value = valueParts.join('=').trim();

                // 따옴표 제거
                if ((value.startsWith('"') && value.endsWith('"')) ||
                    (value.startsWith("'") && value.endsWith("'"))) {
                    value = value.slice(1, -1);
                }

                args[key.trim()] = value;
            }
        }

        return args;
    }

    // 채팅 완성 메서드
    async chatCompletion(messages, options = {}) {
        try {
            console.log('=== LLM Chat Completion Started ===');
            console.log('Current config:', {
                apiType: this.config.apiType,
                apiUrl: this.config.apiUrl,
                model: this.config.model,
                hasApiKey: !!this.config.apiKey
            });

            if (!messages || !Array.isArray(messages) || messages.length === 0) {
                throw new Error('유효한 메시지가 없습니다.');
            }

            if (!this.config.apiType) {
                throw new Error('API 유형이 설정되지 않았습니다.');
            }

            if (!this.config.apiUrl) {
                throw new Error('API URL이 설정되지 않았습니다.');
            }

            const apiType = this.config.apiType.toLowerCase();
            console.log('Using API type:', apiType);

            // API 키 필요 여부 확인
            if ((apiType === 'openai' || apiType === 'anthropic') && !this.config.apiKey) {
                throw new Error(`${apiType.toUpperCase()} API 키가 설정되지 않았습니다.`);
            }

            switch (apiType) {
                case 'openai':
                    return await this.handleOpenAIRequest(messages, options);
                case 'anthropic':
                    return await this.handleAnthropicRequest(messages, options);
                case 'ollama':
                case 'local':
                    return await this.handleOllamaRequest(messages, options);
                case 'llamacpp':
                case 'llama':
                    return await this.handleLlamaCppRequest(messages, options);
                case 'custom':
                    return await this.handleCustomApiRequest(messages, options);
                default:
                    throw new Error(`지원되지 않는 API 유형: ${apiType}`);
            }
        } catch (error) {
            console.error('Chat completion error:', error);
            
            // 오류 메시지 개선
            let errorMessage = error.message;
            if (error.message.includes('HTTP 404')) {
                if (this.config.apiType === 'ollama' || this.config.apiType === 'local') {
                    errorMessage = `Ollama 서버에서 모델 '${this.config.model}'을 찾을 수 없습니다. 'ollama list' 명령으로 설치된 모델을 확인해주세요.`;
                } else {
                    errorMessage = `모델 '${this.config.model}'을 찾을 수 없습니다. 설정에서 올바른 모델명을 확인해주세요.`;
                }
            } else if (error.message.includes('HTTP 401')) {
                errorMessage = 'API 키가 유효하지 않습니다. 설정에서 API 키를 확인해주세요.';
            } else if (error.message.includes('HTTP 429')) {
                errorMessage = 'API 요청 한도를 초과했습니다. 잠시 후 다시 시도해주세요.';
            } else if (error.message.includes('Failed to fetch') || error.message.includes('ECONNREFUSED')) {
                if (this.config.apiType === 'ollama' || this.config.apiType === 'local') {
                    errorMessage = `Ollama 서버에 연결할 수 없습니다. Ollama가 실행 중인지 확인해주세요. (URL: ${this.config.apiUrl})`;
                } else {
                    errorMessage = `서버에 연결할 수 없습니다. API URL(${this.config.apiUrl})을 확인해주세요.`;
                }
            }
            
            throw new Error(errorMessage);
        }
    }

    // OpenAI API 요청 처리 (MCP 도구 지원)
    async handleOpenAIRequest(messages, options = {}) {
        console.log('Using OpenAI API');

        if (!this.config.apiKey) {
            const localApiKey = localStorage.getItem('openai-api-key');
            if (localApiKey) {
                this.config.apiKey = localApiKey;
                console.log('API key retrieved from localStorage');
            } else {
                throw new Error('OpenAI API 키가 설정되지 않았습니다.');
            }
        }

        const requestBody = {
            model: this.config.model,
            messages: messages.map(msg => ({
                role: msg.role,
                content: msg.content
            })),
            temperature: options.temperature || this.config.temperature,
            max_tokens: options.maxTokens || this.config.maxTokens,
            stream: false
        };

        // MCP 도구가 있으면 추가
        if (options.tools && options.tools.length > 0) {
            requestBody.tools = options.tools;
            requestBody.tool_choice = "auto";
            console.log('Added MCP tools to OpenAI request:', options.tools.length);
        }

        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.config.apiKey}`
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();

        // 도구 호출이 있는지 확인
        const message = data.choices?.[0]?.message;
        if (message?.tool_calls && message.tool_calls.length > 0) {
            return await this.handleOpenAIToolCalls(message, messages, options);
        }

        return message?.content || '';
    }

    // OpenAI 도구 호출 처리
    async handleOpenAIToolCalls(message, originalMessages, options) {
        console.log('Processing OpenAI tool calls...');

        let result = message.content || '';

        for (const toolCall of message.tool_calls) {
            console.log(`Executing tool: ${toolCall.function.name}`, toolCall.function.arguments);

            try {
                const args = JSON.parse(toolCall.function.arguments);
                const toolResult = await this.callMcpTool(toolCall.function.name, args);

                result += `\n\n[Tool Result: ${toolCall.function.name}]\n${JSON.stringify(toolResult, null, 2)}\n`;
            } catch (error) {
                console.error(`Tool execution failed: ${toolCall.function.name}`, error);
                result += `\n\n[Tool Error: ${toolCall.function.name}]\n${error.message}\n`;
            }
        }

        return result;
    }

    // Anthropic API 요청 처리 (MCP 도구 지원)
    async handleAnthropicRequest(messages, options = {}) {
        console.log('Using Anthropic API');

        if (!this.config.apiKey) {
            const localApiKey = localStorage.getItem('anthropic-api-key');
            if (localApiKey) {
                this.config.apiKey = localApiKey;
                console.log('API key retrieved from localStorage');
            } else {
                throw new Error('Anthropic API 키가 설정되지 않았습니다.');
            }
        }

        const requestBody = {
            model: this.config.model,
            messages: messages.map(msg => ({
                role: msg.role,
                content: msg.content
            })),
            temperature: options.temperature || this.config.temperature,
            max_tokens: options.maxTokens || this.config.maxTokens
        };

        // MCP 도구가 있으면 추가
        if (options.tools && options.tools.length > 0) {
            requestBody.tools = options.tools;
            console.log('Added MCP tools to Anthropic request:', options.tools.length);
        }

        const response = await fetch('https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': this.config.apiKey,
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();

        // 도구 호출이 있는지 확인
        if (data.content && data.content.some(block => block.type === 'tool_use')) {
            return await this.handleAnthropicToolUse(data, messages, options);
        }

        return data.content?.[0]?.text || '';
    }

    // Anthropic 도구 사용 처리
    async handleAnthropicToolUse(response, originalMessages, options) {
        console.log('Processing Anthropic tool use...');

        let result = '';
        const toolResults = [];

        for (const block of response.content) {
            if (block.type === 'text') {
                result += block.text;
            } else if (block.type === 'tool_use') {
                console.log(`Executing tool: ${block.name}`, block.input);

                try {
                    const toolResult = await this.callMcpTool(block.name, block.input);
                    toolResults.push({
                        tool_use_id: block.id,
                        type: 'tool_result',
                        content: JSON.stringify(toolResult, null, 2)
                    });

                    result += `\n\n[Tool Result: ${block.name}]\n${JSON.stringify(toolResult, null, 2)}\n`;
                } catch (error) {
                    console.error(`Tool execution failed: ${block.name}`, error);
                    toolResults.push({
                        tool_use_id: block.id,
                        type: 'tool_result',
                        content: `Error: ${error.message}`,
                        is_error: true
                    });

                    result += `\n\n[Tool Error: ${block.name}]\n${error.message}\n`;
                }
            }
        }

        return result;
    }

    // Ollama API 요청 처리
    async handleOllamaRequest(messages, options = {}) {
        console.log('Using Ollama API');

        // /api/chat 엔드포인트 시도
        try {
            console.log('Trying Ollama chat endpoint: /api/chat');

            const requestBody = {
                model: this.config.model,
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content
                })),
                stream: false,
                options: {
                    temperature: options.temperature || this.config.temperature,
                    num_predict: options.maxTokens || this.config.maxTokens
                }
            };

            const response = await fetch(`${this.config.apiUrl}/api/chat`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Successfully used Ollama chat endpoint');
                return data.message?.content || data.response || '';
            } else {
                console.log('Ollama chat endpoint failed:', response.status);
            }
        } catch (error) {
            console.log('Ollama chat endpoint failed:', error.message);
        }

        // /api/generate 엔드포인트 시도
        try {
            console.log('Trying Ollama generate endpoint: /api/generate');

            const prompt = messages.map(msg => {
                if (msg.role === 'user') return `User: ${msg.content}`;
                if (msg.role === 'assistant') return `Assistant: ${msg.content}`;
                if (msg.role === 'system') return `System: ${msg.content}`;
                return msg.content;
            }).join('\n\n');

            const requestBody = {
                model: this.config.model,
                prompt: prompt,
                stream: false,
                options: {
                    temperature: options.temperature || this.config.temperature,
                    num_predict: options.maxTokens || this.config.maxTokens
                }
            };

            const response = await fetch(`${this.config.apiUrl}/api/generate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Successfully used Ollama generate endpoint');
                return data.response || '';
            } else {
                const errorText = await response.text();
                throw new Error(`Ollama API 오류: ${response.status} ${errorText}`);
            }
        } catch (error) {
            throw new Error(`Ollama 서버에 연결할 수 없습니다: ${error.message}`);
        }
    }

    // LLaMA.cpp 서버 요청 처리
    async handleLlamaCppRequest(messages, options = {}) {
        console.log('Using LLaMA.cpp server API');

        // /v1/chat/completions 엔드포인트 시도
        try {
            console.log('Trying LLaMA.cpp chat endpoint: /v1/chat/completions');

            const requestBody = {
                model: this.config.model,
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content
                })),
                temperature: options.temperature || this.config.temperature,
                max_tokens: options.maxTokens || this.config.maxTokens,
                stream: false
            };

            const headers = { 'Content-Type': 'application/json' };
            if (this.config.apiKey) {
                headers['Authorization'] = `Bearer ${this.config.apiKey}`;
            }

            const response = await fetch(`${this.config.apiUrl}/v1/chat/completions`, {
                method: 'POST',
                headers,
                body: JSON.stringify(requestBody)
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Successfully used LLaMA.cpp chat endpoint');
                return data.choices?.[0]?.message?.content || '';
            } else {
                console.log('LLaMA.cpp chat endpoint failed:', response.status);
            }
        } catch (error) {
            console.log('LLaMA.cpp chat endpoint failed:', error.message);
        }

        // /completion 엔드포인트 시도
        try {
            console.log('Trying LLaMA.cpp completion endpoint: /completion');

            const lastUserMessage = messages.filter(msg => msg.role === 'user').pop();
            const requestBody = {
                prompt: lastUserMessage ? lastUserMessage.content : '',
                temperature: options.temperature || this.config.temperature,
                n_predict: options.maxTokens || this.config.maxTokens,
                stream: false
            };

            const headers = { 'Content-Type': 'application/json' };
            if (this.config.apiKey) {
                headers['Authorization'] = `Bearer ${this.config.apiKey}`;
            }

            const response = await fetch(`${this.config.apiUrl}/completion`, {
                method: 'POST',
                headers,
                body: JSON.stringify(requestBody)
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Successfully used LLaMA.cpp completion endpoint');
                return data.content || data.text || '';
            } else {
                const errorText = await response.text();
                throw new Error(`LLaMA.cpp API 오류: ${response.status} ${errorText}`);
            }
        } catch (error) {
            throw new Error(`LLaMA.cpp 서버에 연결할 수 없습니다: ${error.message}`);
        }
    }

    // 사용자 정의 API 요청 처리
    async handleCustomApiRequest(messages, options = {}) {
        console.log('Using Custom API');

        // OpenAI 호환 형식 시도
        try {
            console.log('Trying custom API with OpenAI format');

            const requestBody = {
                model: this.config.model,
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content
                })),
                temperature: options.temperature || this.config.temperature,
                max_tokens: options.maxTokens || this.config.maxTokens,
                stream: false
            };

            const headers = { 'Content-Type': 'application/json' };
            if (this.config.apiKey) {
                headers['Authorization'] = `Bearer ${this.config.apiKey}`;
            }

            const response = await fetch(`${this.config.apiUrl}/v1/chat/completions`, {
                method: 'POST',
                headers,
                body: JSON.stringify(requestBody)
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Successfully used custom API with OpenAI format');
                return data.choices?.[0]?.message?.content || data.content || data.text || data.response || '';
            } else {
                console.log('Custom API OpenAI format failed:', response.status);
            }
        } catch (error) {
            console.log('Custom API OpenAI format failed:', error.message);
        }

        // 일반 completion 형식 시도
        try {
            console.log('Trying custom API with completion format');

            const lastUserMessage = messages.filter(msg => msg.role === 'user').pop();
            const requestBody = {
                prompt: lastUserMessage ? lastUserMessage.content : '',
                model: this.config.model,
                temperature: options.temperature || this.config.temperature,
                max_tokens: options.maxTokens || this.config.maxTokens,
                stream: false
            };

            const headers = { 'Content-Type': 'application/json' };
            if (this.config.apiKey) {
                headers['Authorization'] = `Bearer ${this.config.apiKey}`;
            }

            const response = await fetch(`${this.config.apiUrl}/completion`, {
                method: 'POST',
                headers,
                body: JSON.stringify(requestBody)
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Successfully used custom API with completion format');
                return data.content || data.text || data.response || '';
            } else {
                const errorText = await response.text();
                throw new Error(`사용자 정의 API 오류: ${response.status} ${errorText}`);
            }
        } catch (error) {
            throw new Error(`사용자 정의 API에 연결할 수 없습니다: ${error.message}`);
        }
    }

    // 채팅 완성 스트리밍 메서드
    async chatCompletionStream(messages, options = {}) {
        try {
            console.log('=== LLM Chat Completion Stream Started ===');

            if (!messages || !Array.isArray(messages) || messages.length === 0) {
                throw new Error('유효한 메시지가 없습니다.');
            }

            const apiType = this.config.apiType.toLowerCase();
            const onChunk = options.onChunk || (() => {});

            switch (apiType) {
                case 'openai':
                    // OpenAI 스트리밍 구현
                    if (!this.config.apiKey) {
                        throw new Error('OpenAI API 키가 설정되지 않았습니다.');
                    }

                    const requestBody = {
                        model: this.config.model,
                        messages: messages.map(msg => ({
                            role: msg.role,
                            content: msg.content
                        })),
                        temperature: options.temperature || this.config.temperature,
                        max_tokens: options.maxTokens || this.config.maxTokens,
                        stream: true
                    };

                    const response = await fetch('https://api.openai.com/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.config.apiKey}`
                        },
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`HTTP ${response.status}: ${errorText}`);
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder('utf-8');
                    let buffer = '';

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop() || '';

                        for (const line of lines) {
                            if (line.startsWith('data: ') && line !== 'data: [DONE]') {
                                try {
                                    const data = JSON.parse(line.substring(6));
                                    const content = data.choices[0]?.delta?.content || '';
                                    if (content) {
                                        onChunk(content);
                                    }
                                } catch (e) {
                                    console.error('Error parsing streaming response:', e);
                                }
                            }
                        }
                    }
                    break;

                default:
                    // 스트리밍을 지원하지 않는 API는 일반 요청 후 한 번에 전달
                    console.log(`API type ${apiType} does not support streaming, using regular chat completion`);
                    const content = await this.chatCompletion(messages, options);
                    onChunk(content);
                    break;
            }
        } catch (error) {
            console.error('Chat completion stream error:', error);
            throw error;
        }
    }
}

// 전역 객체로 등록
window.LLMClient = LLMClient;

// Node.js 환경에서도 사용할 수 있도록 모듈 내보내기
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LLMClient;
}
