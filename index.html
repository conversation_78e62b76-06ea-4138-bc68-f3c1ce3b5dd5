<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SBT MCP Desktop</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

</head>
<body>
    <!-- 타이틀바 (Windows/Linux용) -->
    <div class="titlebar" id="titlebar">
        <div class="titlebar-drag-region">
            <div class="titlebar-title">SBT MCP Desktop</div>
        </div>
        <div class="titlebar-controls">
            <button class="titlebar-button" id="minimize-btn" title="최소화">
                <i class="fas fa-minus"></i>
            </button>
            <button class="titlebar-button" id="maximize-btn" title="최대화">
                <i class="fas fa-square"></i>
            </button>
            <button class="titlebar-button close" id="close-btn" title="닫기">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- 메인 컨테이너 -->
    <div class="app-container">
        <!-- 사이드바 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo app-logo" style="cursor: pointer;">
                    <i class="fas fa-robot"></i>
                    <span class="app-title" style="cursor: pointer;">SBT MCP Desktop</span>
                </div>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <!-- 채팅 목록 -->
            <div class="chat-list">
                <div class="chat-list-header">
                    <button class="new-chat-btn" id="new-chat-btn">
                        <i class="fas fa-plus"></i>
                        <span>새 채팅</span>
                    </button>
                </div>
                <div class="chat-items" id="chat-items">
                    <!-- 채팅 항목들이 동적으로 추가됩니다 -->
                </div>
            </div>

            <!-- 채팅 항목 템플릿 -->
            <template id="chat-item-template">
                <div class="chat-item" data-chat-id="">
                    <div class="chat-item-icon"></div>
                    <div class="chat-item-content">
                        <div class="chat-item-title"></div>
                        <div class="chat-item-preview"></div>
                    </div>
                    <div class="chat-item-time"></div>
                    <button class="chat-item-menu" data-chat-id="" title="메뉴">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="chat-item-dropdown">
                        <div class="dropdown-item favorite-btn" data-chat-id="">
                            <i class="fas fa-star-o"></i>
                            즐겨찾기
                        </div>
                        <div class="dropdown-item rename-btn" data-chat-id="">
                            <i class="fas fa-edit"></i>
                            이름 변경
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="dropdown-item delete-btn" data-chat-id="">
                            <i class="fas fa-trash"></i>
                            삭제
                        </div>
                    </div>
                </div>
            </template>

            <!-- 사이드바 하단 -->
            <div class="sidebar-footer">
                <button class="settings-btn" id="settings-btn">
                    <i class="fas fa-cog"></i>
                    <span>설정</span>
                </button>
                <button class="profile-btn" id="profile-btn">
                    <i class="fas fa-user"></i>
                    <span>개인정보</span>
                </button>
            </div>
        </div>

        <!-- 메인 컨텐츠 영역 -->
        <div class="main-content">
            <!-- 환영 화면 -->
            <div class="welcome-screen" id="welcome-screen">
                <div class="welcome-content">
                    <div class="welcome-logo">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h1>SBT MCP Desktop에 오신 것을 환영합니다</h1>
                    <p>SBT Global과 함께하는 AI 어시스턴트</p>
                    <div class="quick-actions">
                        <button class="quick-action-btn" data-prompt="안녕하세요!">
                            <i class="fas fa-comment"></i>
                            인사하기
                        </button>
                        <button class="quick-action-btn" data-prompt="오늘 할 일을 정리해주세요">
                            <i class="fas fa-list"></i>
                            할 일 정리
                        </button>
                        <button class="quick-action-btn" data-prompt="코딩 도움이 필요해요">
                            <i class="fas fa-code"></i>
                            코딩 도움
                        </button>
                        <button class="quick-action-btn" data-prompt="창의적인 아이디어를 제안해주세요">
                            <i class="fas fa-lightbulb"></i>
                            아이디어 제안
                        </button>
                        <button id="mcp-test-btn" class="quick-action-btn" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-network-wired"></i>
                            MCP 연결 테스트
                        </button>
                    </div>
                </div>
            </div>

             <!-- 채팅 화면 -->
            <div class="chat-screen" id="chat-screen" style="display: none;">
                <!-- 채팅 헤더 -->
                <div class="chat-header">
                    <div class="chat-title">
                        <h5 id="current-chat-title">새 채팅</h5>
                        <span class="model-info">Llama4 Maverick</span>
                    </div>
                    <div class="chat-actions">
                        <button class="chat-action-btn" id="export-btn" title="대화 내보내기">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="chat-action-btn" id="clear-btn" title="대화 지우기">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- 메시지 컨테이너 -->
                <div class="messages-container" id="messages-container">
                    <div class="messages" id="messages">
                        <!-- 메시지들이 동적으로 추가됩니다 -->
                    </div>
                    <!-- 로딩 인디케이터 간소화 -->
                    <div class="message-loading-indicator" id="message-loading-indicator" style="display: none;">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">잠시만 기다려 주세요.</div>
                    </div>
                </div>

                <!-- 입력 영역 -->
                <div class="input-area">
                    <div id="attachment-previews">
                    </div>
                    <div class="input-container">
                        <div class="input-wrapper">
                            <textarea 
                                id="message-input" 
                                placeholder="메시지를 입력하세요..." 
                                rows="1"
                                maxlength="32000"
                            ></textarea>
                            <div class="input-actions">
                                <button class="attach-btn" id="attach-btn" title="파일 첨부">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                <button class="send-btn" id="send-btn" title="전송" disabled>
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                        <div class="input-footer">
                            <div class="character-count">
                                <span id="char-count">0</span> / 32,000
                                <button class="mcp-toggle-btn" id="mcp-toggle-btn" title="MCP 서버 사용 토글">
                                    <i class="fas fa-network-wired"></i>
                                    <div class="mcp-status-tooltip" id="mcp-status-tooltip">
                                        MCP 서버 상태 확인 중...
                                    </div>
                                </button>
                            </div>
                            <div class="input-hint">
                                Shift + Enter로 줄바꿈, Enter로 전송
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 설정 모달 -->
    <div class="modal" id="settings-modal">
        <div class="modal-content">
            <iframe id="settings-iframe" src="settings.html" frameborder="0"></iframe>
        </div>
    </div>

    <!-- 로딩 오버레이 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span class="loading-text">잠시만 기다려 주세요.</span>
        </div>
    </div>

     <!-- 파일 입력 (숨김) -->
    <input type="file" id="file-input" style="display: none;" multiple accept=".txt,.md,.json,.csv,.pdf">

    <!-- 스크립트 -->
    <script src="llm-client.js"></script>
    <script src="app.js"></script>
    <script src="settings.js"></script>
</body>
</html>
